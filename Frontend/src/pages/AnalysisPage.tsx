import { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Clock, CheckCircle, XCircle, AlertCircle, Eye, RefreshCw } from "lucide-react";
import { ResultsViewer } from "@/components/analysis/ResultsViewer";
import { formatDate } from "@/utils/file";
import { apiClient } from "@/services/api";

interface JobStatus {
  job_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  file_key: string;
  created_at: string;
  completed_at?: string;
  hfo_count?: number;
  error_message?: string;
}

export function AnalysisPage() {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const [jobs, setJobs] = useState<JobStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchJobs = useCallback(async (silent = false) => {
    try {
      if (!silent) setLoading(true);
      else setRefreshing(true);

      // Fetch jobs from the backend API
      const response = await apiClient.get<JobStatus[]>("/analysis/jobs");
      setJobs(response.data || []);
    } catch {
      setJobs([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchJobs();
  }, [fetchJobs]);

  useEffect(() => {
    // Auto-refresh every 30 seconds for pending/processing jobs
    const hasActiveJobs = jobs.some((j) => j.status === "pending" || j.status === "processing");
    if (!hasActiveJobs) return;

    const interval = setInterval(() => {
      fetchJobs(true);
    }, 30000);

    return () => clearInterval(interval);
  }, [jobs, fetchJobs]);

  const getStatusIcon = (status: JobStatus["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case "failed":
        return <XCircle className="w-5 h-5 text-red-600" />;
      case "processing":
        return <Clock className="w-5 h-5 text-blue-600 animate-spin" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: JobStatus["status"]) => {
    const styles = {
      completed: "bg-green-100 text-green-800",
      failed: "bg-red-100 text-red-800",
      processing: "bg-blue-100 text-blue-800",
      pending: "bg-yellow-100 text-yellow-800",
    };

    return <span className={`px-2 py-1 text-xs font-medium rounded-full ${styles[status]}`}>{status.charAt(0).toUpperCase() + status.slice(1)}</span>;
  };

  if (loading) {
    return (
      <div className="p-8 bg-white rounded-lg shadow-sm">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Show results viewer if jobId is in URL
  if (jobId) {
    return (
      <div className="flex flex-col h-full">
        <div className="px-6 py-3 bg-white border-b">
          <button
            onClick={() => navigate('/analysis')}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← Back to Analysis Jobs
          </button>
        </div>
        <div className="flex-1 overflow-hidden">
          <ResultsViewer jobId={jobId} onClose={() => navigate('/analysis')} />
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-white rounded-lg shadow-sm">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-semibold">Analysis Jobs</h1>
          <p className="text-gray-600">View and manage your HFO analysis results</p>
        </div>
        <button
          onClick={() => fetchJobs()}
          disabled={refreshing}
          className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`} />
          Refresh
        </button>
      </div>

      {jobs.length === 0 ? (
        <div className="text-center py-12">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No analysis jobs found</p>
          <p className="text-sm text-gray-500 mt-2">Submit files for processing from the Home page</p>
        </div>
      ) : (
        <div className="space-y-4">
          {jobs.map((job) => (
            <div key={job.job_id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(job.status)}
                  <div>
                    <p className="font-medium text-gray-900">{job.file_key.split("/").pop()}</p>
                    <p className="text-sm text-gray-500">Submitted: {formatDate(job.created_at)}</p>
                    {job.completed_at && <p className="text-sm text-gray-500">Completed: {formatDate(job.completed_at)}</p>}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {job.hfo_count !== undefined && <span className="text-sm font-medium text-gray-700">{job.hfo_count} HFOs</span>}
                  {getStatusBadge(job.status)}
                  {job.status === "completed" && (
                    <button
                      onClick={() => navigate(`/analysis/${job.job_id}`)}
                      className="flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      View Results
                    </button>
                  )}
                </div>
              </div>

              {job.error_message && (
                <div className="mt-3 p-3 bg-red-50 rounded-md">
                  <p className="text-sm text-red-800">{job.error_message}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
