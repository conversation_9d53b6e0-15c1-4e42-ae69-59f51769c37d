import * as Plotly from 'plotly.js';
import type { HFOEvent } from '@/types/hfo';

// ─── GENERATE HFO MARKER TRACES ───
export const generateHFOMarkerTraces = (
  hfoEvents: HFOEvent[],
  channel: string,
  channelIndex: number,
  timeWindow: [number, number],
  channelOffset: number
): Plotly.Data[] => {
  const traces: Plotly.Data[] = [];

  const channelHFOs = hfoEvents.filter(
    hfo => hfo.channel === channel &&
           hfo.start_time >= timeWindow[0] &&
           hfo.start_time <= timeWindow[1]
  );

  if (channelHFOs.length === 0) return traces;

  // ─── START MARKERS ───
  traces.push({
    x: channelHFOs.map(hfo => hfo.start_time),
    y: channelHFOs.map(() => channelIndex * channelOffset),
    type: 'scattergl',
    mode: 'markers',
    name: `${channel} HFOs`,
    marker: {
      color: '#ef4444',
      size: 8,
      symbol: 'diamond',
    },
    showlegend: false,
    hovertemplate: 'HFO Event<br>Time: %{x:.2f}s<br>Peak Freq: %{customdata:.1f}Hz<extra></extra>',
    customdata: channelHFOs.map(hfo => hfo.peak_frequency),
  });

  // ─── DURATION BARS ───
  channelHFOs.forEach(hfo => {
    traces.push({
      x: [hfo.start_time, hfo.end_time],
      y: [channelIndex * channelOffset - 10, channelIndex * channelOffset - 10],
      type: 'scattergl',
      mode: 'lines',
      line: {
        color: 'rgba(239, 68, 68, 0.6)',
        width: 4,
      },
      showlegend: false,
      hovertemplate: `HFO Duration<br>Start: ${hfo.start_time.toFixed(3)}s<br>End: ${hfo.end_time.toFixed(3)}s<br>Duration: ${(
        (hfo.end_time - hfo.start_time) * 1000
      ).toFixed(1)}ms<extra></extra>`,
    });

    // ─── VERTICAL START LINE ───
    traces.push({
      x: [hfo.start_time, hfo.start_time],
      y: [channelIndex * channelOffset - 20, channelIndex * channelOffset + 20],
      type: 'scattergl',
      mode: 'lines',
      line: {
        color: 'rgba(239, 68, 68, 0.3)',
        width: 1,
        dash: 'dot',
      },
      showlegend: false,
      hoverinfo: 'skip',
    });

    // ─── VERTICAL END LINE ───
    traces.push({
      x: [hfo.end_time, hfo.end_time],
      y: [channelIndex * channelOffset - 20, channelIndex * channelOffset + 20],
      type: 'scattergl',
      mode: 'lines',
      line: {
        color: 'rgba(239, 68, 68, 0.3)',
        width: 1,
        dash: 'dot',
      },
      showlegend: false,
      hoverinfo: 'skip',
    });
  });

  return traces;
};

// ─── GENERATE THRESHOLD TRACES ───
export const generateThresholdTraces = (
  channel: string,
  channelIndex: number,
  windowedTime: number[],
  channelOffset: number
): Plotly.Data[] => {
  const traces: Plotly.Data[] = [];

  // ─── UPPER THRESHOLD ───
  const upperThreshold = channelIndex * channelOffset + 30;
  traces.push({
    x: windowedTime,
    y: new Array(windowedTime.length).fill(upperThreshold),
    type: 'scattergl',
    mode: 'lines',
    name: `${channel} upper threshold`,
    line: {
      color: 'rgba(59, 130, 246, 0.3)',
      width: 1,
      dash: 'dot',
    },
    showlegend: false,
    hovertemplate: 'Upper threshold<extra></extra>',
  });

  // ─── LOWER THRESHOLD ───
  const lowerThreshold = channelIndex * channelOffset - 30;
  traces.push({
    x: windowedTime,
    y: new Array(windowedTime.length).fill(lowerThreshold),
    type: 'scattergl',
    mode: 'lines',
    name: `${channel} lower threshold`,
    line: {
      color: 'rgba(59, 130, 246, 0.3)',
      width: 1,
      dash: 'dot',
    },
    showlegend: false,
    hovertemplate: 'Lower threshold<extra></extra>',
  });

  return traces;
};

// ─── GENERATE MAIN CHANNEL TRACE ───
export const generateChannelTrace = (
  channel: string,
  data: number[],
  time: number[],
  channelIndex: number,
  gain: number,
  channelOffset: number
): Plotly.Data => {
  const scaledData = data.map(val => (val * 20) / gain);
  const offsetData = scaledData.map(val => val + channelIndex * channelOffset);

  return {
    x: time,
    y: offsetData,
    type: 'scattergl',
    mode: 'lines',
    name: channel,
    line: {
      color: '#1e293b',
      width: 1,
      simplify: false,
    },
    hovertemplate: `${channel}<br>Time: %{x:.2f}s<br>Amplitude: %{y:.2f}μV<extra></extra>`,
  };
};