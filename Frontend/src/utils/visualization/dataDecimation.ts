// ─── <PERSON><PERSON>A DECIMATION UTILITIES ───

interface DecimationConfig {
  maxPointsPerChannel: number;
  preservePeaks: boolean;
}

export const getDecimationConfig = (windowDuration: number): DecimationConfig => {
  if (windowDuration <= 10) {
    return { maxPointsPerChannel: 50000, preservePeaks: true };
  } else if (windowDuration <= 60) {
    return { maxPointsPerChannel: 20000, preservePeaks: true };
  } else if (windowDuration <= 300) {
    return { maxPointsPerChannel: 10000, preservePeaks: true };
  } else {
    return { maxPointsPerChannel: 5000, preservePeaks: false };
  }
};

export const decimateData = (data: number[], maxPoints: number, preservePeaks = true): number[] => {
  if (data.length <= maxPoints) return data;

  const step = Math.ceil(data.length / maxPoints);
  const decimated: number[] = [];

  for (let i = 0; i < data.length; i += step) {
    const windowEnd = Math.min(i + step, data.length);

    if (preservePeaks) {
      // ─── PRESERVE PEAKS BY USING MAX ABSOLUTE VALUE ───
      let maxVal = data[i];
      for (let j = i + 1; j < windowEnd; j++) {
        if (Math.abs(data[j]) > Math.abs(maxVal)) {
          maxVal = data[j];
        }
      }
      decimated.push(maxVal);
    } else {
      // ─── SIMPLE AVERAGE ───
      let sum = 0;
      for (let j = i; j < windowEnd; j++) {
        sum += data[j];
      }
      decimated.push(sum / (windowEnd - i));
    }
  }

  return decimated;
};

export const decimateTimeAxis = (timeAxis: number[], decimationFactor: number): number[] => {
  const decimated: number[] = [];
  for (let i = 0; i < timeAxis.length; i += decimationFactor) {
    decimated.push(timeAxis[i]);
  }
  return decimated;
};