import type { AnalysisParameters } from '@/components/analysis/EnhancedAnalysisParametersForm';

interface ApiParameters {
  thresholds: {
    amplitude1: number;
    amplitude2: number;
    peaks1: number;
    peaks2: number;
    duration: number;
    temporal_sync: number;
    spatial_sync: number;
  };
  montage: string;
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  analysis_start: number;
  analysis_end: number;
  channelSelection: {
    selectedChannels: string[];
  };
}

// ─── BUILD API PARAMETERS ───
export const buildAnalysisApiParams = (
  parameters: AnalysisParameters,
  channelList: string[]
): ApiParameters => {
  const selectedChannels = parameters.channelSelection?.selectedChannels;
  const resolvedChannels = selectedChannels && selectedChannels.length > 0
    ? selectedChannels
    : channelList;

  // Convert montage object to string for API
  const montageString = parameters.montage.reference
    ? `${parameters.montage.type}:${parameters.montage.reference}`
    : parameters.montage.type;

  return {
    thresholds: {
      amplitude1: parameters.thresholds.amplitude_1,
      amplitude2: parameters.thresholds.amplitude_2,
      peaks1: parameters.thresholds.peaks_1,
      peaks2: parameters.thresholds.peaks_2,
      duration: parameters.thresholds.duration,
      temporal_sync: parameters.thresholds.temporal_sync,
      spatial_sync: parameters.thresholds.spatial_sync,
    },
    montage: montageString,
    frequency: {
      low_cutoff: parameters.frequency.low_cutoff,
      high_cutoff: parameters.frequency.high_cutoff,
    },
    analysis_start: parameters.analysis_start ?? 0,
    analysis_end: parameters.analysis_end ?? 0,
    channelSelection: {
      selectedChannels: resolvedChannels,
    },
  };
};

// ─── BUILD BATCH ANALYSIS PAYLOAD ───
export const buildBatchAnalysisPayload = (
  fileKeys: string[],
  parameters: AnalysisParameters,
  getChannelsForFile: (fileKey: string) => string[]
) => {
  return {
    files: fileKeys.map((fileKey) => ({
      file_key: fileKey,
      parameters: buildAnalysisApiParams(
        parameters,
        getChannelsForFile(fileKey)
      ),
    })),
  };
};