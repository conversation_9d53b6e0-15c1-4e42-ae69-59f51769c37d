import type { HFOEvent, HFOType } from '@/types/hfo';

interface HfoStats {
  total: number;
  accepted: number;
  rejected: number;
  rejected_long: number;
  lfo_rejected: number;
  noise_rejected: number;
}

// ─── CALCULATE HFO STATISTICS BY TYPE ───
export const calculateHfoStatsByType = (hfoEvents: HFOEvent[]): HfoStats => {
  return {
    total: hfoEvents.length,
    accepted: hfoEvents.filter(e => (e.type || 'accepted') === 'accepted').length,
    rejected: hfoEvents.filter(e => e.type === 'rejected').length,
    rejected_long: hfoEvents.filter(e => e.type === 'rejected_long').length,
    lfo_rejected: hfoEvents.filter(e => e.type === 'lfo_rejected').length,
    noise_rejected: hfoEvents.filter(e => e.type === 'noise_rejected').length,
  };
};

// ─── FILTER HFO EVENTS BY TYPE ───
export const filterHfoEventsByType = (
  hfoEvents: HFOEvent[],
  visibleTypes: HFOType[]
): HFOEvent[] => {
  return hfoEvents.filter(event => {
    const eventType = event.type || 'accepted';
    return visibleTypes.includes(eventType);
  });
};

// ─── CALCULATE HFO TIME DISTRIBUTION ───
export const calculateHfoTimeDistribution = (
  hfoEvents: HFOEvent[],
  totalDuration: number,
  windowSize: number = 10
): Record<string, number> => {
  const timeDistribution: Record<string, number> = {};

  // ─── INITIALIZE TIME WINDOWS ───
  for (let i = 0; i < totalDuration; i += windowSize) {
    const windowKey = `${i}-${i + windowSize}s`;
    timeDistribution[windowKey] = 0;
  }

  // ─── COUNT HFOS PER WINDOW ───
  hfoEvents.forEach(hfo => {
    const windowStart = Math.floor(hfo.start_time / windowSize) * windowSize;
    const windowKey = `${windowStart}-${windowStart + windowSize}s`;
    if (timeDistribution[windowKey] !== undefined) {
      timeDistribution[windowKey]++;
    }
  });

  return timeDistribution;
};

// ─── CALCULATE HFO COUNT BY CHANNEL ───
export const calculateHfoCountByChannel = (
  hfoEvents: HFOEvent[],
  channels: string[]
): Record<string, number> => {
  const counts: Record<string, number> = {};

  channels.forEach(channel => {
    counts[channel] = hfoEvents.filter(e => e.channel === channel).length;
  });

  return counts;
};

// ─── ANALYZE HFO TYPE DISTRIBUTION ───
export const analyzeHfoTypes = (
  hfoEvents: HFOEvent[]
): Record<string, number> => {
  const typeCounts: Record<string, number> = {};

  hfoEvents.forEach(event => {
    const eventType = event.type || 'undefined';
    typeCounts[eventType] = (typeCounts[eventType] || 0) + 1;
  });

  return typeCounts;
};