import { UPLOAD_CONFIG } from '@/constants';

interface ChunkSettings {
  chunkSize: number;
  totalParts: number;
  concurrency: number;
}

// ─── CALCULATE OPTIMAL CHUNK SETTINGS ───
export const calculateOptimalSettings = (fileSize: number): ChunkSettings => {
  const { DYNAMIC_SIZING, MIN_PART_SIZE, MAX_PART_SIZE, TARGET_PARTS } = UPLOAD_CONFIG.MULTIPART;

  let chunkSize: number;
  let concurrency: number;

  // ─── DETERMINE SETTINGS BY FILE SIZE ───
  if (fileSize <= DYNAMIC_SIZING.SMALL.maxSize) {
    chunkSize = DYNAMIC_SIZING.SMALL.chunkSize;
    concurrency = DYNAMIC_SIZING.SMALL.concurrency;
  } else if (fileSize <= DYNAMIC_SIZING.MEDIUM.maxSize) {
    chunkSize = DYNAMIC_SIZING.MEDIUM.chunkSize;
    concurrency = DYNAMIC_SIZING.MEDIUM.concurrency;
  } else {
    chunkSize = DYNAMIC_SIZING.LARGE.chunkSize;
    concurrency = DYNAMIC_SIZING.LARGE.concurrency;
  }

  // ─── HANDLE VERY SMALL FILES ───
  if (fileSize < MIN_PART_SIZE * 2) {
    chunkSize = fileSize;
  } else {
    // ─── CALCULATE BASED ON TARGET PARTS ───
    const targetChunkSize = Math.ceil(fileSize / TARGET_PARTS);
    chunkSize = Math.min(Math.max(chunkSize, MIN_PART_SIZE), MAX_PART_SIZE);

    if (targetChunkSize > MIN_PART_SIZE && targetChunkSize < MAX_PART_SIZE) {
      chunkSize = targetChunkSize;
    }
  }

  const totalParts = Math.ceil(fileSize / chunkSize);

  return { chunkSize, totalParts, concurrency };
};

// ─── CALCULATE PART OFFSETS ───
export const calculatePartOffsets = (
  partNumber: number,
  chunkSize: number,
  fileSize: number
): { start: number; end: number } => {
  const start = (partNumber - 1) * chunkSize;
  const end = Math.min(start + chunkSize, fileSize);
  return { start, end };
};