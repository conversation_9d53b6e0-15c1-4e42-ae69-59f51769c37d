import { fileService } from '@/services/api';
import type { CompletedPart } from '@/types/api';
import { calculatePartOffsets } from './chunkCalculator';

interface UploadPartParams {
  file: File;
  key: string;
  uploadId: string;
  partNumber: number;
  chunkSize: number;
  onProgress?: (partNumber: number, loaded: number) => void;
  retryCount?: number;
}

// ─── UPLOAD SINGLE PART WITH RETRY ───
export const uploadPart = async (
  params: UploadPartParams
): Promise<CompletedPart> => {
  const { file, key, uploadId, partNumber, chunkSize, onProgress, retryCount = 0 } = params;
  const maxRetries = 3;

  try {
    // ─── CALCULATE PART BOUNDARIES ───
    const { start, end } = calculatePartOffsets(partNumber, chunkSize, file.size);
    const chunk = file.slice(start, end);

    // ─── GET PRESIGNED URL ───
    const { url } = await fileService.getPartUploadUrl(
      key,
      uploadId,
      partNumber
    );

    // ─── UPLOAD CHUNK ───
    const response = await uploadChunkToS3(
      url,
      chunk,
      (loaded) => onProgress?.(partNumber, loaded)
    );

    return {
      ETag: response.headers.get('ETag') || '',
      PartNumber: partNumber,
    };
  } catch (error) {
    if (retryCount < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
      return uploadPart({ ...params, retryCount: retryCount + 1 });
    }
    throw error;
  }
};

// ─── UPLOAD CHUNK TO S3 ───
const uploadChunkToS3 = async (
  url: string,
  chunk: Blob,
  onProgress?: (loaded: number) => void
): Promise<Response> => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        onProgress?.(event.loaded);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status >= 200 && xhr.status < 300) {
        resolve(new Response(xhr.response, {
          status: xhr.status,
          headers: new Headers({
            ETag: xhr.getResponseHeader('ETag') || '',
          }),
        }));
      } else {
        reject(new Error(`Upload failed with status ${xhr.status}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error during upload'));
    });

    xhr.open('PUT', url);
    xhr.send(chunk);
  });
};

// ─── BATCH UPLOAD PARTS ───
export const uploadPartsInBatch = async (
  file: File,
  key: string,
  uploadId: string,
  startPart: number,
  endPart: number,
  chunkSize: number,
  onProgress?: (partNumber: number, loaded: number, total: number) => void
): Promise<CompletedPart[]> => {
  const promises = [];

  for (let partNumber = startPart; partNumber <= endPart; partNumber++) {
    const { start, end } = calculatePartOffsets(partNumber, chunkSize, file.size);
    const partSize = end - start;

    promises.push(
      uploadPart({
        file,
        key,
        uploadId,
        partNumber,
        chunkSize,
        onProgress: (part, loaded) => onProgress?.(part, loaded, partSize),
      })
    );
  }

  return Promise.all(promises);
};