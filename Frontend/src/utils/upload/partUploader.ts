import { UPLOAD_CONFIG } from '@/constants';

// ─── UPLOAD PART WITH RETRY ───
export const uploadPartWithRetry = async (
  blob: Blob,
  url: string,
  partNumber: number,
  onProgress: (loaded: number) => void
): Promise<string> => {
  const { RETRY, TIMEOUTS } = UPLOAD_CONFIG.MULTIPART;

  return new Promise((resolve, reject) => {
    const attempt = (retries: number, delayMs: number) => {
      const xhr = new XMLHttpRequest();

      xhr.open("PUT", url);
      xhr.timeout = TIMEOUTS.PART_UPLOAD;

      xhr.upload.onprogress = (ev: ProgressEvent) => {
        if (ev.lengthComputable) {
          onProgress(ev.loaded);
        }
      };

      const handleError = (reason: string) => {
        if (retries > 0) {
          onProgress(0);

          const jitter = Math.random() * 0.3 + 0.85;
          const nextDelay = Math.min(
            delayMs * RETRY.BACKOFF_MULTIPLIER * jitter,
            RETRY.MAX_DELAY
          );

          setTimeout(() => attempt(retries - 1, nextDelay), delayMs);
        } else {
          reject(new Error(reason));
        }
      };

      xhr.onerror = () => handleError(`Network error uploading part ${partNumber}`);
      xhr.ontimeout = () => handleError(`Timeout uploading part ${partNumber}`);

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          const eTag = xhr.getResponseHeader("ETag") || "";
          resolve(eTag.replaceAll('"', ""));
        } else {
          handleError(`Part ${partNumber} upload failed (HTTP ${xhr.status})`);
        }
      };

      xhr.send(blob);
    };

    attempt(RETRY.MAX_ATTEMPTS - 1, RETRY.INITIAL_DELAY);
  });
};

// ─── WORKER POOL EXECUTOR ───
export const executeWithWorkerPool = async (
  totalTasks: number,
  workerCount: number,
  taskFn: (taskNumber: number) => Promise<void>
): Promise<void> => {
  const queue = Array.from({ length: totalTasks }, (_, i) => i + 1);
  const errors: Error[] = [];

  const workers = Array.from({ length: Math.min(workerCount, totalTasks) }, async () => {
    while (queue.length > 0) {
      const taskNumber = queue.shift();
      if (!taskNumber) break;

      try {
        await taskFn(taskNumber);
      } catch (error) {
        errors.push(error as Error);

        if (errors.length >= Math.ceil(totalTasks * 0.3)) {
          queue.length = 0;
        }
      }
    }
  });

  await Promise.all(workers);

  if (errors.length > 0) {
    throw new Error(`Upload failed: ${errors[0].message} (and ${errors.length - 1} more errors)`);
  }
};