import { fileService } from '@/services/api';

// ─── PREFETCH PART URLS ───
export const prefetchPartUrls = async (
  key: string,
  uploadId: string,
  totalParts: number
): Promise<Map<number, string>> => {
  const allPartNumbers = Array.from({ length: totalParts }, (_, i) => i + 1);
  const maxBatchSize = 100;
  const urlMap = new Map<number, string>();

  if (totalParts <= maxBatchSize) {
    const batch = await fileService.getBatchPartUploadUrls(key, uploadId, allPartNumbers);
    batch.parts.forEach(p => urlMap.set(p.part_number, p.url));
  } else {
    const batches = [];
    for (let i = 0; i < totalParts; i += maxBatchSize) {
      const batchPartNumbers = allPartNumbers.slice(i, i + maxBatchSize);
      batches.push(fileService.getBatchPartUploadUrls(key, uploadId, batchPartNumbers));
    }

    const results = await Promise.all(batches);
    results.forEach(batch => {
      batch.parts.forEach(p => urlMap.set(p.part_number, p.url));
    });
  }

  return urlMap;
};