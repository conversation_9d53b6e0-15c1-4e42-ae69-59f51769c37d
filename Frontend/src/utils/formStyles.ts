// Form styling utilities
export const formStyles = {
  input: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",
  label: "flex items-center gap-2 text-sm font-medium",
  select: "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",
  checkbox: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
  button: {
    primary: "px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",
    secondary: "px-4 py-2 bg-gray-200 text-gray-800 font-medium rounded-md hover:bg-gray-300 disabled:opacity-50",
    danger: "px-4 py-2 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 disabled:opacity-50"
  },
  card: {
    wrapper: "rounded-lg border border-gray-200 bg-white shadow-sm",
    header: "px-6 py-4 border-b border-gray-200",
    content: "p-6"
  },
  grid: {
    cols2: "grid grid-cols-2 gap-4",
    cols3: "grid grid-cols-3 gap-4",
    cols4: "grid grid-cols-4 gap-4"
  }
} as const;

// Form field wrapper component styles
export const fieldStyles = {
  wrapper: "space-y-2",
  error: "text-red-600 text-sm mt-1",
  hint: "text-gray-500 text-xs mt-1",
  required: "text-red-500"
} as const;