// Parameter validation utilities
interface ValidationRule {
  min?: number;
  max?: number;
  required?: boolean;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

interface ValidationRules {
  [key: string]: ValidationRule;
}

export const parameterRules: ValidationRules = {
  lowCutoff: { min: 1, max: 1000, required: true },
  highCutoff: { min: 1, max: 1000, required: true },
  amplitude1: { min: 1, max: 10, required: true },
  amplitude2: { min: 1, max: 10, required: true },
  peaks1: { min: 1, max: 20, required: true },
  peaks2: { min: 1, max: 20, required: true },
  duration: { min: 1, max: 100, required: true },
  temporalSync: { min: 1, max: 100, required: true },
  spatialSync: { min: 1, max: 1000, required: true },
  analysisStart: { min: 0 },
  analysisEnd: { min: 0 }
};

export function validateField(value: any, rule: ValidationRule): string | null {
  if (rule.required && !value) {
    return "This field is required";
  }

  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return `Value must be at least ${rule.min}`;
    }
    if (rule.max !== undefined && value > rule.max) {
      return `Value must be at most ${rule.max}`;
    }
  }

  if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
    return "Invalid format";
  }

  if (rule.custom) {
    return rule.custom(value);
  }

  return null;
}

export function validateFrequencyRange(lowCutoff: number, highCutoff: number): string | null {
  if (lowCutoff >= highCutoff) {
    return "High cutoff must be greater than low cutoff";
  }
  return null;
}

export function validateTimeSegment(start?: number, end?: number, duration?: number): string | null {
  if (start !== undefined && end !== undefined) {
    if (start >= end) {
      return "End time must be greater than start time";
    }
  }
  if (start !== undefined && start < 0) {
    return "Start time cannot be negative";
  }
  if (duration !== undefined && duration <= 0) {
    return "Duration must be positive";
  }
  return null;
}