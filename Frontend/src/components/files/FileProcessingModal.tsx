import React from 'react';
import { Setting<PERSON>, Loader2 } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { EnhancedAnalysisParametersForm, type AnalysisParameters } from '@/components/analysis/EnhancedAnalysisParametersForm';
import { AnalysisParametersForm } from '@/components/analysis/AnalysisParametersForm';
import { FileDetailsStep } from '@/components/analysis/FileDetailsStep';
import type { FileInfo } from '@/types/api';

interface FileProcessingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  modalStep: 'details' | 'parameters';
  onStepChange: (step: 'details' | 'parameters') => void;
  useEnhancedForm: boolean;
  fetchingMetadata: boolean;
  files: FileInfo[];
  selectedFiles: string[];
  activeFileKey: string | null;
  onFileChange: (fileKey: string) => void;
  onSubmit: (params: AnalysisParameters) => void;
  processing: boolean;
  fileMetadata: {
    channels: string[];
    samplingRate: number;
    duration: number;
  } | null;
}

export const FileProcessingModal: React.FC<FileProcessingModalProps> = ({
  open,
  onOpenChange,
  modalStep,
  onStepChange,
  useEnhancedForm,
  fetchingMetadata,
  files,
  selectedFiles,
  activeFileKey,
  onFileChange,
  onSubmit,
  processing,
  fileMetadata
}) => {
  const selectedFilesList = files.filter(f => selectedFiles.includes(f.key));
  const activeFile = activeFileKey ? selectedFilesList.find(f => f.key === activeFileKey) : selectedFilesList[0];

  return (
    <Dialog
      open={open}
      onOpenChange={(value) => {
        onOpenChange(value);
        if (!value) onStepChange('details');
      }}
    >
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {modalStep === 'details' ? 'File Details Overview' : 'Configure HFO Analysis Parameters'}
          </DialogTitle>
          <DialogDescription>
            {modalStep === 'details'
              ? 'Review file information before configuring analysis parameters'
              : useEnhancedForm
              ? 'Configure comprehensive parameters matching your analysis requirements'
              : 'Customize the parameters for detecting High-Frequency Oscillations (HFOs) in the selected files.'}
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          {fetchingMetadata && useEnhancedForm ? (
            // ─── LOADING STATE ───
            <div className="flex flex-col items-center justify-center py-12 space-y-4">
              <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-2">Loading EDF File Data</h3>
                <p className="text-sm text-gray-600">
                  Analyzing {activeFile?.filename || 'selected file'}...
                </p>
                <p className="text-xs text-gray-500 mt-2">This may take a few seconds.</p>
              </div>
            </div>
          ) : modalStep === 'details' && useEnhancedForm ? (
            // ─── FILE DETAILS STEP ───
            <FileDetailsStep
              files={selectedFilesList}
              activeFileKey={activeFileKey}
              onFileChange={onFileChange}
              fetchingMetadata={fetchingMetadata}
              onNext={() => onStepChange('parameters')}
              onCancel={() => {
                onOpenChange(false);
                onStepChange('details');
              }}
            />
          ) : useEnhancedForm ? (
            // ─── ENHANCED PARAMETERS FORM ───
            <EnhancedAnalysisParametersForm
              onParametersChange={() => {}}
              onSubmit={onSubmit}
              isSubmitting={processing}
              channels={fileMetadata?.channels || getDefaultChannels()}
              fileInfo={{
                filename: activeFile?.filename || '',
                samplingRate: fileMetadata?.samplingRate || 256,
                duration: fileMetadata?.duration || 600,
              }}
            />
          ) : (
            // ─── BASIC PARAMETERS FORM ───
            <AnalysisParametersForm
              onParametersChange={() => {}}
              onSubmit={onSubmit}
              isSubmitting={processing}
              channels={[]}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

// ─── DEFAULT CHANNELS ───
const getDefaultChannels = () => [
  'FP1-F7', 'F7-T3', 'T3-T5', 'T5-O1',
  'FP2-F8', 'F8-T4', 'T4-T6', 'T6-O2',
  'FP1-F3', 'F3-C3', 'C3-P3', 'P3-O1',
  'FP2-F4', 'F4-C4', 'C4-P4', 'P4-O2',
];