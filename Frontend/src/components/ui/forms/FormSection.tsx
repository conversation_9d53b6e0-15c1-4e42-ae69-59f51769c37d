import React from 'react';
import { cn } from '@/lib/utils';

interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className,
  titleClassName,
  collapsible = false,
  defaultCollapsed = false,
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  const handleToggle = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {title && (
        <div
          className={cn(
            "border-b pb-2",
            collapsible && "cursor-pointer select-none"
          )}
          onClick={handleToggle}
        >
          <div className="flex items-center justify-between">
            <h3 className={cn(
              "text-lg font-semibold text-gray-900",
              titleClassName
            )}>
              {title}
            </h3>
            {collapsible && (
              <button
                type="button"
                className="text-gray-500 hover:text-gray-700"
                aria-label={isCollapsed ? "Expand section" : "Collapse section"}
              >
                <svg
                  className={cn(
                    "h-5 w-5 transition-transform",
                    isCollapsed ? "-rotate-90" : ""
                  )}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            )}
          </div>
          {description && (
            <p className="mt-1 text-sm text-gray-600">{description}</p>
          )}
        </div>
      )}
      {(!collapsible || !isCollapsed) && (
        <div className="space-y-4">
          {children}
        </div>
      )}
    </div>
  );
};