import React from 'react';
import { cn } from '@/lib/utils';
import { LoadingSpinner } from './LoadingSpinner';

interface LoadingOverlayProps {
  visible: boolean;
  message?: string;
  className?: string;
  spinnerSize?: 'sm' | 'md' | 'lg' | 'xl';
  fullScreen?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  visible,
  message = 'Loading...',
  className,
  spinnerSize = 'lg',
  fullScreen = false,
}) => {
  if (!visible) return null;

  return (
    <div
      className={cn(
        "flex items-center justify-center bg-white bg-opacity-90",
        fullScreen ? "fixed inset-0 z-50" : "absolute inset-0 z-10",
        className
      )}
    >
      <div className="text-center">
        <LoadingSpinner size={spinnerSize} />
        {message && (
          <p className="mt-4 text-sm text-gray-600">{message}</p>
        )}
      </div>
    </div>
  );
};