import React from 'react';
import { cn } from '@/lib/utils';

interface ErrorMessageProps {
  error?: string | Error | null;
  title?: string;
  className?: string;
  variant?: 'inline' | 'banner' | 'toast';
  onDismiss?: () => void;
}

export const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  title = 'Error',
  className,
  variant = 'inline',
  onDismiss,
}) => {
  if (!error) return null;

  const errorMessage = typeof error === 'string' ? error : error.message;

  const baseClasses = "rounded-md p-4";
  const variantClasses = {
    inline: "bg-red-50 border border-red-200",
    banner: "bg-red-600 text-white",
    toast: "bg-red-100 border-l-4 border-red-500",
  };

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant],
        className
      )}
      role="alert"
    >
      <div className="flex">
        <div className="flex-shrink-0">
          <svg
            className={cn(
              "h-5 w-5",
              variant === 'banner' ? "text-white" : "text-red-400"
            )}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className={cn(
            "text-sm font-medium",
            variant === 'banner' ? "text-white" : "text-red-800"
          )}>
            {title}
          </h3>
          <div className={cn(
            "mt-2 text-sm",
            variant === 'banner' ? "text-red-100" : "text-red-700"
          )}>
            <p>{errorMessage}</p>
          </div>
        </div>
        {onDismiss && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                onClick={onDismiss}
                className={cn(
                  "inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2",
                  variant === 'banner'
                    ? "bg-red-600 text-red-100 hover:bg-red-500 focus:ring-offset-red-600 focus:ring-white"
                    : "bg-red-50 text-red-500 hover:bg-red-100 focus:ring-offset-red-50 focus:ring-red-600"
                )}
              >
                <span className="sr-only">Dismiss</span>
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};