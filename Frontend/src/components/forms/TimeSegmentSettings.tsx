import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormField } from './FormField';

interface TimeSegmentSettingsProps {
  analysisStart?: number;
  analysisEnd?: number;
  onUpdate: (field: string, value: number | string) => void;
}

export const TimeSegmentSettings: React.FC<TimeSegmentSettingsProps> = ({
  analysisStart,
  analysisEnd,
  onUpdate,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Time Segment</CardTitle>
        <CardDescription>Define the analysis time window (in seconds)</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-4">
        <FormField
          id="analysis-start"
          label="Start Time (s)"
          type="number"
          value={analysisStart || 0}
          onChange={(value) => onUpdate('analysis_start', value ? Number(value) : "")}
          min={0}
          step={0.1}
          placeholder="0 (beginning)"
        />
        <FormField
          id="analysis-end"
          label="End Time (s)"
          type="number"
          value={analysisEnd || ""}
          onChange={(value) => onUpdate('analysis_end', value ? Number(value) : "")}
          min={0}
          step={0.1}
          placeholder="Auto (end of file)"
          tooltip="Leave empty for entire file"
        />
      </CardContent>
    </Card>
  );
};