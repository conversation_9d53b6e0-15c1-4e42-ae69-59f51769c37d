import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormField } from './FormField';

interface MontageSettingsProps {
  montage: {
    type: "bipolar" | "average" | "referential";
    reference?: string;
  };
  channels: string[];
  onUpdate: (field: string, value: string) => void;
}

export const MontageSettings: React.FC<MontageSettingsProps> = ({
  montage,
  channels,
  onUpdate,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Montage Configuration</CardTitle>
        <CardDescription>Select the montage type for analysis</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormField
          id="montage-type"
          label="Montage Type"
          type="select"
          value={montage.type}
          onChange={(value) => onUpdate('type', String(value))}
        >
          <option value="bipolar">Bipolar</option>
          <option value="average">Average</option>
          <option value="referential">Referential</option>
        </FormField>

        {montage.type === "referential" && (
          <FormField
            id="reference-channel"
            label="Reference Channel"
            type="select"
            value={montage.reference || ""}
            onChange={(value) => onUpdate('reference', String(value))}
          >
            <option value="">Select reference channel</option>
            {channels.map((channel) => (
              <option key={channel} value={channel}>
                {channel}
              </option>
            ))}
          </FormField>
        )}
      </CardContent>
    </Card>
  );
};