import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormField } from './FormField';

interface ThresholdSettingsProps {
  thresholds: {
    amplitude_1: number;
    amplitude_2: number;
    peaks_1: number;
    peaks_2: number;
    duration: number;
    temporal_sync: number;
    spatial_sync: number;
  };
  onUpdate: (field: string, value: number) => void;
}

export const ThresholdSettings: React.FC<ThresholdSettingsProps> = ({
  thresholds,
  onUpdate,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Detection Thresholds</CardTitle>
        <CardDescription>Fine-tune HFO detection sensitivity</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-4">
        <FormField
          id="amplitude-1"
          label="Energy Threshold (SD)"
          type="number"
          value={thresholds.amplitude_1}
          onChange={(value) => onUpdate('amplitude_1', Number(value))}
          min={1}
          max={10}
          step={0.5}
          tooltip="Standard deviations above mean energy"
        />
        <FormField
          id="amplitude-2"
          label="Baseline Threshold (SD)"
          type="number"
          value={thresholds.amplitude_2}
          onChange={(value) => onUpdate('amplitude_2', Number(value))}
          min={1}
          max={10}
          step={0.5}
          tooltip="Standard deviations above baseline EEG"
        />
        <FormField
          id="peaks-1"
          label="Min Peaks"
          type="number"
          value={thresholds.peaks_1}
          onChange={(value) => onUpdate('peaks_1', Number(value))}
          min={1}
          max={20}
        />
        <FormField
          id="peaks-2"
          label="Min Peaks Above Baseline"
          type="number"
          value={thresholds.peaks_2}
          onChange={(value) => onUpdate('peaks_2', Number(value))}
          min={1}
          max={20}
        />
        <FormField
          id="duration"
          label="Min Duration (ms)"
          type="number"
          value={thresholds.duration}
          onChange={(value) => onUpdate('duration', Number(value))}
          min={1}
          max={100}
        />
        <FormField
          id="temporal-sync"
          label="Min Separation (ms)"
          type="number"
          value={thresholds.temporal_sync}
          onChange={(value) => onUpdate('temporal_sync', Number(value))}
          min={1}
          max={100}
        />
        <FormField
          id="spatial-sync"
          label="Channel Delay (ms)"
          type="number"
          value={thresholds.spatial_sync}
          onChange={(value) => onUpdate('spatial_sync', Number(value))}
          min={1}
          max={1000}
          className="col-span-2"
          tooltip="Max delay between channels for connectivity"
        />
      </CardContent>
    </Card>
  );
};