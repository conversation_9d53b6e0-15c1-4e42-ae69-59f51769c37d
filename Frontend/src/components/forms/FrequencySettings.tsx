import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings } from 'lucide-react';
import { FormField } from './FormField';

interface FrequencySettingsProps {
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  onUpdate: (field: string, value: number) => void;
}

export const FrequencySettings: React.FC<FrequencySettingsProps> = ({
  frequency,
  onUpdate,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Frequency Settings
        </CardTitle>
        <CardDescription>Configure the frequency band for HFO detection</CardDescription>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-4">
        <FormField
          id="low-cutoff"
          label="Low Cutoff (Hz)"
          type="number"
          value={frequency.low_cutoff}
          onChange={(value) => onUpdate('low_cutoff', Number(value))}
          min={1}
          max={1000}
          tooltip="Lower frequency bound for bandpass filter"
        />
        <FormField
          id="high-cutoff"
          label="High Cutoff (Hz)"
          type="number"
          value={frequency.high_cutoff}
          onChange={(value) => onUpdate('high_cutoff', Number(value))}
          min={1}
          max={1000}
          tooltip="Upper frequency bound for bandpass filter"
        />
      </CardContent>
    </Card>
  );
};