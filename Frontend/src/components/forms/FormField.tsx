import React from 'react';
import { Info } from 'lucide-react';

interface FormFieldProps {
  id: string;
  label: string;
  type?: 'number' | 'text' | 'select';
  value: string | number;
  onChange: (value: string | number) => void;
  min?: number;
  max?: number;
  step?: number;
  placeholder?: string;
  tooltip?: string;
  children?: React.ReactNode; // For select options
  className?: string;
}

const inputClass = "w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent";
const labelClass = "flex items-center gap-2 text-sm font-medium";

export const FormField: React.FC<FormFieldProps> = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  min,
  max,
  step,
  placeholder,
  tooltip,
  children,
  className = '',
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <label htmlFor={id} className={labelClass}>
        {label}
        {tooltip && (
          <span title={tooltip}>
            <Info className="h-3 w-3 text-gray-400" />
          </span>
        )}
      </label>
      {type === 'select' ? (
        <select
          id={id}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={inputClass}
        >
          {children}
        </select>
      ) : (
        <input
          id={id}
          type={type}
          value={value}
          onChange={(e) => onChange(type === 'number' ? Number(e.target.value) : e.target.value)}
          min={min}
          max={max}
          step={step}
          placeholder={placeholder}
          className={inputClass}
        />
      )}
    </div>
  );
};