import React, { useState, useEffect } from 'react';
import { FormSection } from '@/components/ui/forms/FormSection';
import { ActionButton } from '@/components/ui/buttons/ActionButton';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';

interface ChannelSelectionProps {
  channels: string[];
  selectedChannels: string[];
  onChannelToggle: (channel: string) => void;
  onSelectAll: () => void;
  onDeselectAll: () => void;
}

export const ChannelSelection: React.FC<ChannelSelectionProps> = ({
  channels,
  selectedChannels,
  onChannelToggle,
  onSelectAll,
  onDeselectAll,
}) => {
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Group channels
  const groupChannelsByPrefix = (channels: string[]): Record<string, string[]> => {
    const groups: Record<string, string[]> = {};
    const excludedTypes = ['EKG', 'REF', 'E', 'C'];

    channels.forEach((channel) => {
      let prefix = 'Other';

      const originalMatch = channel.match(/^(P|POL)\s([A-Za-z]+)/);
      if (originalMatch) {
        prefix = originalMatch[2];
      } else {
        const simpleMatch = channel.match(/^([A-Za-z]+)/);
        if (simpleMatch) {
          prefix = simpleMatch[1];
        }
      }

      if (excludedTypes.includes(prefix.toUpperCase())) {
        return;
      }

      if (!groups[prefix]) {
        groups[prefix] = [];
      }
      groups[prefix].push(channel);
    });

    const sortedGroups: Record<string, string[]> = {};
    Object.keys(groups)
      .sort()
      .forEach((key) => {
        sortedGroups[key] = groups[key].sort();
      });

    return sortedGroups;
  };

  const channelGroups = groupChannelsByPrefix(channels);

  useEffect(() => {
    if (channels.length > 0) {
      const allGroups = Object.keys(channelGroups);
      const expandedState: Record<string, boolean> = {};
      allGroups.forEach((group) => {
        expandedState[group] = true;
      });
      setExpandedGroups(expandedState);
    }
  }, [channels, channelGroups]);

  const handleGroupSelection = (groupPrefix: string, select: boolean) => {
    const groupChannels = channelGroups[groupPrefix];
    groupChannels.forEach(channel => {
      if (select && !selectedChannels.includes(channel)) {
        onChannelToggle(channel);
      } else if (!select && selectedChannels.includes(channel)) {
        onChannelToggle(channel);
      }
    });
  };

  const isGroupFullySelected = (groupPrefix: string) => {
    const groupChannels = channelGroups[groupPrefix];
    return groupChannels.every((ch) => selectedChannels.includes(ch));
  };

  const isGroupPartiallySelected = (groupPrefix: string) => {
    const groupChannels = channelGroups[groupPrefix];
    const selectedInGroup = groupChannels.filter((ch) => selectedChannels.includes(ch));
    return selectedInGroup.length > 0 && selectedInGroup.length < groupChannels.length;
  };

  const toggleGroupExpansion = (groupPrefix: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupPrefix]: !prev[groupPrefix],
    }));
  };

  return (
    <FormSection
      title="Channel Selection"
      description={`${selectedChannels.length} of ${channels.length} channels selected`}
    >
      <div className="space-y-4">
        <div className="flex gap-2">
          <ActionButton
            size="sm"
            variant="secondary"
            onClick={onSelectAll}
          >
            Select All
          </ActionButton>
          <ActionButton
            size="sm"
            variant="secondary"
            onClick={onDeselectAll}
          >
            Deselect All
          </ActionButton>
        </div>

        <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded-md p-3">
          {Object.entries(channelGroups).map(([groupPrefix, groupChannels]) => (
            <div key={groupPrefix} className="space-y-2">
              <div className="flex items-center gap-2">
                <button
                  type="button"
                  onClick={() => toggleGroupExpansion(groupPrefix)}
                  className="p-0.5 hover:bg-gray-100 rounded"
                >
                  {expandedGroups[groupPrefix] ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>
                <Checkbox
                  checked={isGroupFullySelected(groupPrefix) || isGroupPartiallySelected(groupPrefix)}
                  onCheckedChange={(checked) => handleGroupSelection(groupPrefix, !!checked)}
                />
                <span className="font-medium text-sm">
                  {groupPrefix} ({groupChannels.length} channels)
                </span>
              </div>

              {expandedGroups[groupPrefix] && (
                <div className="ml-8 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                  {groupChannels.map((channel) => (
                    <label
                      key={channel}
                      className="flex items-center gap-2 text-sm cursor-pointer hover:bg-gray-50 p-1 rounded"
                    >
                      <Checkbox
                        checked={selectedChannels.includes(channel)}
                        onCheckedChange={() => onChannelToggle(channel)}
                      />
                      <span>{channel}</span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </FormSection>
  );
};