import React from 'react';
import { FormInput } from '@/components/ui/forms/FormInput';
import { FormSection } from '@/components/ui/forms/FormSection';

interface FrequencySettings {
  low_cutoff: number;
  high_cutoff: number;
}

interface FrequencySettingsProps {
  frequency: FrequencySettings;
  onUpdate: (field: string, value: number) => void;
}

export const FrequencySettingsForm: React.FC<FrequencySettingsProps> = ({
  frequency,
  onUpdate,
}) => {
  return (
    <FormSection
      title="Frequency Band"
      description="Configure the frequency range for HFO detection"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          label="Low Cutoff (Hz)"
          type="number"
          step="1"
          min="1"
          max={frequency.high_cutoff - 1}
          value={frequency.low_cutoff}
          onChange={(e) => onUpdate('low_cutoff', parseInt(e.target.value))}
          helperText="Lower frequency boundary"
        />

        <FormInput
          label="High Cutoff (Hz)"
          type="number"
          step="1"
          min={frequency.low_cutoff + 1}
          max="1000"
          value={frequency.high_cutoff}
          onChange={(e) => onUpdate('high_cutoff', parseInt(e.target.value))}
          helperText="Upper frequency boundary"
        />

        <div className="md:col-span-2">
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-600">
              Common ranges:
              <span className="ml-2 font-medium">Ripples: 80-250 Hz</span>
              <span className="ml-2 font-medium">Fast Ripples: 250-500 Hz</span>
            </p>
          </div>
        </div>
      </div>
    </FormSection>
  );
};