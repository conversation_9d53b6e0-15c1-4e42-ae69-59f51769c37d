import React from 'react';
import { FormInput } from '@/components/ui/forms/FormInput';
import { FormSelect } from '@/components/ui/forms/FormSelect';
import { FormSection } from '@/components/ui/forms/FormSection';

interface TimeSegment {
  mode: 'entire_file' | 'start_end_times' | 'start_duration';
  startTime?: number;
  endTime?: number;
  duration?: number;
}

interface TimeSegmentSelectorProps {
  timeSegment: TimeSegment;
  onUpdate: (field: string, value: string | number | undefined) => void;
  fileDuration?: number;
}

export const TimeSegmentSelector: React.FC<TimeSegmentSelectorProps> = ({
  timeSegment,
  onUpdate,
  fileDuration,
}) => {
  const modeOptions = [
    { value: 'entire_file', label: 'Analyze Entire File' },
    { value: 'start_end_times', label: 'Specify Start and End Times' },
    { value: 'start_duration', label: 'Specify Start Time and Duration' },
  ];

  const handleModeChange = (mode: string) => {
    onUpdate('mode', mode);
    if (mode === 'entire_file') {
      onUpdate('startTime', undefined);
      onUpdate('endTime', undefined);
      onUpdate('duration', undefined);
    }
  };

  return (
    <FormSection
      title="Time Segment"
      description="Select the portion of the file to analyze"
    >
      <div className="space-y-4">
        <FormSelect
          label="Analysis Mode"
          value={timeSegment.mode}
          onChange={(e) => handleModeChange(e.target.value)}
          options={modeOptions}
        />

        {timeSegment.mode === 'start_end_times' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Start Time (seconds)"
              type="number"
              step="0.1"
              min="0"
              max={fileDuration}
              value={timeSegment.startTime || 0}
              onChange={(e) => onUpdate('startTime', parseFloat(e.target.value))}
              required
            />
            <FormInput
              label="End Time (seconds)"
              type="number"
              step="0.1"
              min={timeSegment.startTime || 0}
              max={fileDuration}
              value={timeSegment.endTime || fileDuration || 0}
              onChange={(e) => onUpdate('endTime', parseFloat(e.target.value))}
              required
            />
          </div>
        )}

        {timeSegment.mode === 'start_duration' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormInput
              label="Start Time (seconds)"
              type="number"
              step="0.1"
              min="0"
              max={fileDuration}
              value={timeSegment.startTime || 0}
              onChange={(e) => onUpdate('startTime', parseFloat(e.target.value))}
              required
            />
            <FormInput
              label="Duration (seconds)"
              type="number"
              step="0.1"
              min="0.1"
              max={fileDuration ? fileDuration - (timeSegment.startTime || 0) : undefined}
              value={timeSegment.duration || 60}
              onChange={(e) => onUpdate('duration', parseFloat(e.target.value))}
              required
            />
          </div>
        )}

        {fileDuration && (
          <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-600">
              File duration: <span className="font-medium">{fileDuration.toFixed(1)} seconds</span>
              {timeSegment.mode !== 'entire_file' && timeSegment.startTime !== undefined && (
                <>
                  <br />
                  Analysis range: <span className="font-medium">
                    {timeSegment.startTime.toFixed(1)} - {
                      timeSegment.mode === 'start_end_times'
                        ? (timeSegment.endTime || fileDuration).toFixed(1)
                        : (timeSegment.startTime + (timeSegment.duration || 60)).toFixed(1)
                    } seconds
                  </span>
                </>
              )}
            </p>
          </div>
        )}
      </div>
    </FormSection>
  );
};