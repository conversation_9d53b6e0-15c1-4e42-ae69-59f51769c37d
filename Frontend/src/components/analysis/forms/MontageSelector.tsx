import React from 'react';
import { FormSelect } from '@/components/ui/forms/FormSelect';
import { FormInput } from '@/components/ui/forms/FormInput';
import { FormSection } from '@/components/ui/forms/FormSection';

interface Montage {
  type: 'bipolar' | 'average' | 'referential';
  reference?: string;
}

interface MontageSelectorProps {
  montage: Montage;
  onUpdate: (field: string, value: string | undefined) => void;
  channels?: string[];
}

export const MontageSelector: React.FC<MontageSelectorProps> = ({
  montage,
  onUpdate,
}) => {
  const montageOptions = [
    { value: 'bipolar', label: 'Bipolar' },
    { value: 'average', label: 'Average Reference' },
    { value: 'referential', label: 'Referential' },
  ];

  const handleMontageChange = (type: string) => {
    onUpdate('type', type);
    if (type !== 'referential') {
      onUpdate('reference', undefined);
    }
  };

  return (
    <FormSection
      title="Montage Configuration"
      description="Select the reference montage for analysis"
    >
      <div className="space-y-4">
        <FormSelect
          label="Montage Type"
          value={montage.type}
          onChange={(e) => handleMontageChange(e.target.value)}
          options={montageOptions}
        />

        {montage.type === 'referential' && (
          <FormInput
            label="Reference Channel"
            type="text"
            value={montage.reference || ''}
            onChange={(e) => onUpdate('reference', e.target.value || undefined)}
            placeholder="Enter reference channel name"
            helperText="Specify the reference channel for referential montage"
            required
          />
        )}

        <div className="p-3 bg-gray-50 border border-gray-200 rounded-md">
          <div className="text-sm text-gray-600 space-y-1">
            <p><strong>Bipolar:</strong> Compares adjacent channels</p>
            <p><strong>Average:</strong> References to average of all channels</p>
            <p><strong>Referential:</strong> References to specific channel</p>
          </div>
        </div>
      </div>
    </FormSection>
  );
};