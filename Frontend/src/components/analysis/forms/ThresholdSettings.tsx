import React from 'react';
import { FormInput } from '@/components/ui/forms/FormInput';
import { FormSection } from '@/components/ui/forms/FormSection';
import { Info } from 'lucide-react';

interface ThresholdSettings {
  amplitude_1: number;
  amplitude_2: number;
  peaks_1: number;
  peaks_2: number;
  duration: number;
  temporal_sync: number;
  spatial_sync: number;
}

interface ThresholdSettingsProps {
  thresholds: ThresholdSettings;
  onUpdate: (field: string, value: number) => void;
}

export const ThresholdSettingsForm: React.FC<ThresholdSettingsProps> = ({
  thresholds,
  onUpdate,
}) => {
  return (
    <FormSection
      title="Thresholds"
      description="Configure HFO detection thresholds and criteria"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormInput
          label="Amplitude 1 (StdDev)"
          type="number"
          step="0.1"
          value={thresholds.amplitude_1}
          onChange={(e) => onUpdate('amplitude_1', parseFloat(e.target.value))}
          helperText="StdDev above energy signal"
        />

        <FormInput
          label="Amplitude 2 (StdDev)"
          type="number"
          step="0.1"
          value={thresholds.amplitude_2}
          onChange={(e) => onUpdate('amplitude_2', parseFloat(e.target.value))}
          helperText="StdDev above baseline"
        />

        <FormInput
          label="Min Peaks 1"
          type="number"
          step="1"
          min="1"
          value={thresholds.peaks_1}
          onChange={(e) => onUpdate('peaks_1', parseInt(e.target.value))}
          helperText="Min peaks in HFO"
        />

        <FormInput
          label="Min Peaks 2"
          type="number"
          step="1"
          min="1"
          value={thresholds.peaks_2}
          onChange={(e) => onUpdate('peaks_2', parseInt(e.target.value))}
          helperText="Min peaks above threshold"
        />

        <FormInput
          label="Duration (ms)"
          type="number"
          step="1"
          min="1"
          value={thresholds.duration}
          onChange={(e) => onUpdate('duration', parseInt(e.target.value))}
          helperText="Min HFO length"
        />

        <FormInput
          label="Temporal Sync (ms)"
          type="number"
          step="1"
          min="0"
          value={thresholds.temporal_sync}
          onChange={(e) => onUpdate('temporal_sync', parseInt(e.target.value))}
          helperText="Inter-HFO interval in any channel"
        />

        <FormInput
          label="Spatial Sync (ms)"
          type="number"
          step="1"
          min="0"
          value={thresholds.spatial_sync}
          onChange={(e) => onUpdate('spatial_sync', parseInt(e.target.value))}
          helperText="Inter-HFO interval across channels"
        />

        <div className="md:col-span-2">
          <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <Info className="h-4 w-4 text-blue-600 flex-shrink-0" />
            <span className="text-sm text-blue-800">
              Adjust detection sensitivity with these thresholds. Lower values = more sensitive detection.
            </span>
          </div>
        </div>
      </div>
    </FormSection>
  );
};