import React, { useMemo } from 'react';
import { ChevronLeft, ChevronRight, RotateCcw, ZoomIn, ZoomOut, Maximize2 } from 'lucide-react';

const MIN_WINDOW_SECONDS = 0.5;
const PAN_STEP_RATIO = 0.2;
const ZOOM_IN_FACTOR = 0.8;
const ZOOM_OUT_FACTOR = 1.25;

interface TimeNavigationControlsProps {
  timeWindow: [number, number];
  timeWindowSize: number;
  totalDuration: number;
  onTimeWindowChange: (window: [number, number]) => void;
  onTimeWindowSizeChange: (size: number) => void;
  onReset: () => void;
  onFullscreen?: () => void;
  currentChunk?: number;
  totalChunks?: number;
}

export const TimeNavigationControls: React.FC<TimeNavigationControlsProps> = ({
  timeWindow,
  timeWindowSize,
  totalDuration,
  onTimeWindowChange,
  onTimeWindowSizeChange,
  onReset,
  onFullscreen,
  currentChunk = 0,
  totalChunks = Math.max(1, Math.ceil(totalDuration / 10)),
}) => {
  const canNavigatePrev = timeWindow[0] > 0;
  const canNavigateNext = timeWindow[1] < totalDuration;
  const effectiveDuration = totalDuration || 0;
  const minWindow = effectiveDuration > 0 ? Math.min(MIN_WINDOW_SECONDS, effectiveDuration) : MIN_WINDOW_SECONDS;
  const canZoomIn = timeWindowSize - minWindow > 1e-3;
  const canZoomOut = effectiveDuration > 0 && effectiveDuration - timeWindowSize > 1e-3;
  const isFullView = effectiveDuration > 0 && Math.abs(timeWindowSize - effectiveDuration) <= 1e-3;
  const displayChunk = Math.max(1, Math.min(currentChunk + 1, totalChunks));

  const presetValues = useMemo(() => {
    const bases = [1, 2, 3, 4, 5, 10].filter((value) => value < effectiveDuration - 1e-6);
    if (effectiveDuration > 0) {
      bases.push(effectiveDuration);
    }
    return bases;
  }, [effectiveDuration]);

  const selectValue = useMemo(() => {
    const match = presetValues.find((value) => Math.abs(value - timeWindowSize) <= 1e-3);
    return match !== undefined ? match.toString() : 'custom';
  }, [presetValues, timeWindowSize]);

  const handleNavigate = (direction: 'prev' | 'next') => {
    if (effectiveDuration === 0 || timeWindowSize === 0) {
      return;
    }

    const step = Math.max(timeWindowSize * PAN_STEP_RATIO, effectiveDuration * 0.01);
    if (direction === 'prev') {
      const newStart = Math.max(0, timeWindow[0] - step);
      onTimeWindowChange([newStart, newStart + timeWindowSize]);
    } else {
      const maxStart = Math.max(0, effectiveDuration - timeWindowSize);
      const newStart = Math.min(maxStart, timeWindow[0] + step);
      onTimeWindowChange([newStart, newStart + timeWindowSize]);
    }
  };

  const handleZoom = (direction: 'in' | 'out') => {
    if (effectiveDuration === 0 || timeWindowSize === 0) {
      return;
    }

    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const factor = direction === 'in' ? ZOOM_IN_FACTOR : ZOOM_OUT_FACTOR;
    const proposed = timeWindowSize * factor;
    const bounded = direction === 'in'
      ? Math.max(minWindow, proposed)
      : Math.min(effectiveDuration, proposed);

    const maxStart = Math.max(0, effectiveDuration - bounded);
    const newStart = Math.max(0, Math.min(maxStart, center - bounded / 2));
    onTimeWindowSizeChange(bounded);
    onTimeWindowChange([newStart, newStart + bounded]);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3">
      <div className="flex items-center justify-between gap-4">
        {/* Navigation Controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleNavigate('prev')}
            disabled={!canNavigatePrev}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Previous (←)"
          >
            <ChevronLeft className="w-4 h-4" />
          </button>

          <div className="flex flex-col items-center px-3">
            {isFullView ? (
              <span className="text-sm font-semibold">Full signal view</span>
            ) : (
              <>
                <span className="text-xs text-gray-500">Window {displayChunk} of {totalChunks}</span>
                <span className="text-sm font-semibold">
                  {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
                </span>
              </>
            )}
          </div>

          <button
            onClick={() => handleNavigate('next')}
            disabled={!canNavigateNext}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Next (→)"
          >
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>

        {/* Window Size Selector */}
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-500">Window Size:</span>
          <select
            value={selectValue}
            onChange={(e) => {
              if (e.target.value === 'custom') {
                return;
              }

              const newSize = Number(e.target.value);
              const currentCenter = (timeWindow[0] + timeWindow[1]) / 2;
              const maxStart = Math.max(0, effectiveDuration - newSize);
              const newStart = Math.max(0, Math.min(maxStart, currentCenter - newSize / 2));
              onTimeWindowSizeChange(newSize);
              onTimeWindowChange([newStart, newStart + newSize]);
            }}
            className="px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
          >
            {presetValues.map((value) => (
              <option key={value} value={value}>
                {Math.abs(value - effectiveDuration) <= 1e-3 ? 'Full signal' : `${value}s`}
              </option>
            ))}
            <option value="custom" disabled>
              Custom
            </option>
          </select>
        </div>

        {/* Zoom Controls */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => handleZoom('in')}
            disabled={!canZoomIn}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Zoom In (+/=)"
          >
            <ZoomIn className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleZoom('out')}
            disabled={!canZoomOut}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            title="Zoom Out (-)"
          >
            <ZoomOut className="w-4 h-4" />
          </button>
        </div>

        {/* Position Indicator */}
        <div className="flex-1 max-w-xs">
          <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="absolute h-full bg-black rounded-full transition-all duration-300"
              style={{
                left: effectiveDuration ? `${(timeWindow[0] / effectiveDuration) * 100}%` : '0%',
                width: effectiveDuration ? `${((timeWindow[1] - timeWindow[0]) / effectiveDuration) * 100}%` : '100%',
              }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>0s</span>
            <span>{effectiveDuration.toFixed(0)}s</span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-1">
          <button
            onClick={onReset}
            className="p-2 rounded hover:bg-gray-100 transition-colors"
            title="Reset View"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          {onFullscreen && (
            <button
              onClick={onFullscreen}
              className="p-2 rounded hover:bg-gray-100 transition-colors"
              title="Fullscreen (F)"
            >
              <Maximize2 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className="mt-2 pt-2 border-t border-gray-100">
        <div className="text-xs text-gray-500">
          Keyboard shortcuts: <span className="font-medium">←/→</span> Pan • <span className="font-medium">+/−</span> Zoom • <span className="font-medium">Home/End</span> Jump • <span className="font-medium">F</span> Fullscreen • <span className="font-medium">Ctrl/⌘/Shift + Scroll</span> Zoom
        </div>
      </div>
    </div>
  );
};
