import React from 'react';
import { Card } from '@/components/ui/card';
import type { HFOEvent } from '@/types/hfo';

interface HfoStatisticsProps {
  hfoEvents: HFOEvent[];
  timeWindow: [number, number];
  selectedChannels: string[];
  samplingRate: number;
}

export const HfoStatistics: React.FC<HfoStatisticsProps> = ({
  hfoEvents,
  timeWindow,
  selectedChannels,
  samplingRate,
}) => {
  // ─── CALCULATE HFO COUNT IN VIEW ───
  const hfoCountInView = hfoEvents.filter(
    h => h.start_time >= timeWindow[0] &&
         h.start_time <= timeWindow[1] &&
         selectedChannels.includes(h.channel)
  ).length;

  return (
    <Card className="p-4">
      <div className="flex justify-between text-sm">
        <div>
          <span className="text-gray-600">Total HFOs in view: </span>
          <span className="font-semibold">{hfoCountInView}</span>
        </div>
        <div>
          <span className="text-gray-600">Channels displayed: </span>
          <span className="font-semibold">{selectedChannels.length}</span>
        </div>
        <div>
          <span className="text-gray-600">Sampling rate: </span>
          <span className="font-semibold">{samplingRate} Hz</span>
        </div>
      </div>
    </Card>
  );
};