import React from 'react';
import { Card } from '@/components/ui/card';
import { TooltipProvider } from '@/components/ui/tooltip';

// Control components
import { ZoomControls } from './controls/ZoomControls';
import { NavigationControls } from './controls/NavigationControls';
import { TimeWindowPresets } from './controls/TimeWindowPresets';
import { GainControl } from './controls/GainControl';
import { DisplayControls } from './controls/DisplayControls';
import { ActionButtons } from './controls/ActionButtons';
import { TimeSlider } from './controls/TimeSlider';
import { KeyboardShortcutsHint } from './controls/KeyboardShortcutsHint';

interface PlotControlPanelProps {
  // Time window controls
  timeWindow: [number, number];
  onTimeWindowChange: (window: [number, number]) => void;
  duration: number;

  // Zoom controls
  onZoomIn: () => void;
  onZoomOut: () => void;
  onReset: () => void;

  // Display controls
  gain: number;
  onGainChange: (gain: number) => void;
  showHFOMarkers: boolean;
  onToggleHFOMarkers: () => void;
  showThresholds: boolean;
  onToggleThresholds: () => void;

  // Navigation
  onNavigate?: (direction: 'prev' | 'next') => void;

  // Export
  onExport?: () => void;

  // Fullscreen
  onFullscreen?: () => void;
  isFullscreen?: boolean;

  // Optional keyboard shortcuts display
  showKeyboardHints?: boolean;
}

export const PlotControlPanel: React.FC<PlotControlPanelProps> = ({
  timeWindow,
  onTimeWindowChange,
  duration,
  onZoomIn,
  onZoomOut,
  onReset,
  gain,
  onGainChange,
  showHFOMarkers,
  onToggleHFOMarkers,
  showThresholds,
  onToggleThresholds,
  onNavigate,
  onExport,
  onFullscreen,
  isFullscreen = false,
  showKeyboardHints = true
}) => {
  const canNavigatePrev = timeWindow[0] > 0;
  const canNavigateNext = timeWindow[1] < duration;

  return (
    <TooltipProvider>
      <Card className="p-4 space-y-4">
        {/* Main controls section */}
        <div className="flex items-center justify-between gap-4">
          {/* Zoom and navigation */}
          <div className="flex items-center gap-2">
            <ZoomControls
              onZoomIn={onZoomIn}
              onZoomOut={onZoomOut}
              onReset={onReset}
              showKeyboardHints={showKeyboardHints}
            />

            {onNavigate && (
              <NavigationControls
                onNavigate={onNavigate}
                canNavigatePrev={canNavigatePrev}
                canNavigateNext={canNavigateNext}
                showKeyboardHints={showKeyboardHints}
              />
            )}
          </div>

          {/* Time window presets */}
          <TimeWindowPresets
            timeWindow={timeWindow}
            duration={duration}
            onTimeWindowChange={onTimeWindowChange}
          />

          {/* Gain control */}
          <GainControl
            gain={gain}
            onGainChange={onGainChange}
          />

          {/* Display toggles */}
          <DisplayControls
            showHFOMarkers={showHFOMarkers}
            onToggleHFOMarkers={onToggleHFOMarkers}
            showThresholds={showThresholds}
            onToggleThresholds={onToggleThresholds}
          />

          {/* Action buttons */}
          <ActionButtons
            onExport={onExport}
            onFullscreen={onFullscreen}
            isFullscreen={isFullscreen}
            showKeyboardHints={showKeyboardHints}
          />
        </div>

        {/* Time slider section */}
        <TimeSlider
          timeWindow={timeWindow}
          duration={duration}
          onTimeWindowChange={onTimeWindowChange}
        />

        {/* Keyboard shortcuts */}
        {showKeyboardHints && <KeyboardShortcutsHint />}
      </Card>
    </TooltipProvider>
  );
};