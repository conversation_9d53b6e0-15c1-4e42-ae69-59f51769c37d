import React, { useEffect, useState, useRef, useMemo } from "react";
import { analysisService } from "@/services/analysisService";
import type { HFOType } from "@/types/hfo";
import type { AnalysisResults } from "@/types/analysis";

// Sub-components
import { ResultsHeader } from "./results/ResultsHeader";
import { ResultsLoadingState } from "./results/ResultsLoadingState";
import { ResultsErrorState } from "./results/ResultsErrorState";
import { ChannelGrid } from "./ChannelGrid";
import { ChannelSelector } from "./ChannelSelector";
import { TimeNavigationControls } from "./TimeNavigationControls";

// Hooks
import { useTimeWindow } from "@/hooks/useTimeWindow";
import { useFullscreen } from "@/hooks/useFullscreen";
import { useKeyboardShortcuts, COMMON_SHORTCUTS } from "@/hooks/useKeyboardShortcuts";
import { useResultsDownload } from "@/hooks/useResultsDownload";

// Utils
import {
  calculateHfoStatsByType,
  filterHfoEventsByType,
  calculateHfoTimeDistribution,
  calculateHfoCountByChannel,
  analyzeHfoTypes
} from "@/utils/analysis/hfoStatistics";

const CHUNK_SECONDS = 10;

interface ResultsViewerProps {
  jobId: string;
  onClose?: () => void;
}

export const ResultsViewer: React.FC<ResultsViewerProps> = ({ jobId, onClose }) => {
  // Refs
  const channelViewRef = useRef<HTMLDivElement>(null);

  // State
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
  const [visibleHFOTypes] = useState<HFOType[]>(["accepted", "rejected", "rejected_long", "lfo_rejected", "noise_rejected"]);
  const [gain, setGain] = useState(1.0);

  const duration = results?.metadata.duration_seconds ?? 0;

  // Custom hooks
  const timeWindowControls = useTimeWindow({
    duration,
    initialWindow: duration > 0 ? [0, Math.min(5, duration)] : undefined,
    minWindowSize: 0.5
  });
  const {
    timeWindow,
    timeWindowSize,
    currentChunk,
    setWindow,
    zoomWindow,
    panWindow,
    jumpToBoundary
  } = timeWindowControls;

  const { isFullscreen, toggleFullscreen } = useFullscreen(channelViewRef);
  const { downloadResults } = useResultsDownload(jobId, results);

  // Gain adjustment functions
  const adjustGain = (factor: number) => {
    setGain(prevGain => {
      const newGain = prevGain * factor;
      // Clamp between 0.1 and 10
      return Math.max(0.1, Math.min(10, newGain));
    });
  };

  // Calculated values
  const hfoStatsByType = useMemo(() => {
    if (!results) return null;
    return calculateHfoStatsByType(results.hfo_events);
  }, [results]);

  const filteredHfoEvents = useMemo(() => {
    if (!results) return [];
    return filterHfoEventsByType(results.hfo_events, visibleHFOTypes);
  }, [results, visibleHFOTypes]);

  // Fetch results - using useEffect directly to avoid dependency issues
  useEffect(() => {
    let mounted = true;

    const fetchResults = async () => {
      try {
        setLoading(true);
        setError(null);

        const results = await analysisService.getAnalysisResults(jobId);

        if (!mounted) return; // Don't update state if unmounted

        if (!results || !results.metadata) {
          throw new Error("Invalid results format");
        }

        // Analyze HFO distribution
        if (results.hfo_events && results.hfo_events.length > 0) {
          calculateHfoTimeDistribution(
            results.hfo_events,
            results.metadata.duration_seconds || 60
          );
          analyzeHfoTypes(results.hfo_events);
        }

        if (results.metadata.channels && results.metadata.channels.length > 0) {
          setSelectedChannels(results.metadata.channels);
        }

        const responseDuration = results.metadata.duration_seconds || 0;
        setWindow(0, Math.min(5, responseDuration > 0 ? responseDuration : 5));

        setResults(results);
        setLoading(false);
      } catch (err) {
        if (!mounted) return; // Don't update state if unmounted

        const error = err as { message?: string; response?: { data?: { detail?: string } } };
        setError(error.message || error.response?.data?.detail || "Failed to load results");
        setLoading(false);
      }
    };

    fetchResults();

    return () => {
      mounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobId]); // Only re-fetch when jobId changes - setWindow excluded intentionally

  // Keyboard shortcuts
  useKeyboardShortcuts([
    { key: COMMON_SHORTCUTS.NAVIGATE_LEFT, handler: () => panWindow(-1) },
    { key: COMMON_SHORTCUTS.NAVIGATE_RIGHT, handler: () => panWindow(1) },
    { key: COMMON_SHORTCUTS.NAVIGATE_UP, handler: () => adjustGain(1.2) }, // Increase amplitude by 20%
    { key: COMMON_SHORTCUTS.NAVIGATE_DOWN, handler: () => adjustGain(0.8) }, // Decrease amplitude by 20%
    { key: COMMON_SHORTCUTS.ZOOM_IN, handler: () => zoomWindow('in') },
    { key: COMMON_SHORTCUTS.ZOOM_OUT, handler: () => zoomWindow('out') },
    { key: COMMON_SHORTCUTS.HOME, handler: () => jumpToBoundary('start') },
    { key: COMMON_SHORTCUTS.END, handler: () => jumpToBoundary('end') },
    { key: COMMON_SHORTCUTS.FULLSCREEN, handler: toggleFullscreen }
  ], { enabled: !!results });

  // Wheel zoom handler
  useEffect(() => {
    const container = channelViewRef.current;
    if (!container || !results) return;

    const handleWheel = (event: WheelEvent) => {
      if (!event.ctrlKey && !event.metaKey && !event.shiftKey) return;

      event.preventDefault();
      const bounds = container.getBoundingClientRect();
      const positionRatio = bounds.width > 0 ? (event.clientX - bounds.left) / bounds.width : 0.5;
      const anchor = timeWindow[0] + (timeWindow[1] - timeWindow[0]) * positionRatio;
      zoomWindow(event.deltaY < 0 ? 'in' : 'out', anchor);
    };

    container.addEventListener('wheel', handleWheel, { passive: false });
    return () => container.removeEventListener('wheel', handleWheel);
  }, [results, zoomWindow, timeWindow]);

  // Render states
  if (loading) return <ResultsLoadingState />;
  if (error) return <ResultsErrorState error={error} />;
  if (!results) return null;

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Header */}
      <ResultsHeader
        filename={results.metadata.filename}
        onDownload={downloadResults}
        onClose={onClose}
      />

      {/* Main graph view */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Time navigation controls */}
        <div className="px-4 pt-4">
          <TimeNavigationControls
            timeWindow={timeWindow}
            timeWindowSize={timeWindowSize}
            totalDuration={results.metadata.duration_seconds}
            currentChunk={currentChunk}
            totalChunks={Math.max(1, Math.ceil(results.metadata.duration_seconds / CHUNK_SECONDS))}
            onTimeWindowChange={(window) => setWindow(window[0], window[1] - window[0], { updateSize: false })}
            onTimeWindowSizeChange={(size) => {
              const currentCenter = (timeWindow[0] + timeWindow[1]) / 2;
              const newStart = currentCenter - size / 2;
              setWindow(newStart, size);
            }}
            onReset={() => {
              const fullDuration = results.metadata.duration_seconds || 0;
              setWindow(0, fullDuration);
              setSelectedChannels(results.metadata.channels);
            }}
            onFullscreen={toggleFullscreen}
          />

          {/* Gain control indicator */}
          <div className="mt-2 flex items-center gap-4">
            <div className="flex items-center gap-2 bg-white rounded-lg px-3 py-1.5 shadow-sm border border-gray-200">
              <span className="text-sm font-medium text-gray-700">Amplitude Gain:</span>
              <span className="text-sm font-bold text-gray-900">{gain.toFixed(1)}x</span>
              <div className="flex items-center gap-1 ml-2">
                <button
                  onClick={() => adjustGain(0.8)}
                  className="px-2 py-0.5 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                  title="Decrease amplitude (Down arrow)"
                >
                  −
                </button>
                <button
                  onClick={() => adjustGain(1.2)}
                  className="px-2 py-0.5 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
                  title="Increase amplitude (Up arrow)"
                >
                  +
                </button>
              </div>
              <span className="text-xs text-gray-500 ml-2">↑/↓ keys to adjust</span>
            </div>
            {gain !== 1.0 && (
              <button
                onClick={() => setGain(1.0)}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              >
                Reset
              </button>
            )}
          </div>

          {/* Position indicator */}
          <div className="mt-2 bg-gray-100 rounded p-2">
            <div className="relative h-2 bg-gray-300 rounded">
              <div
                className="absolute h-full bg-black rounded transition-all duration-300"
                style={{
                  left: `${(timeWindow[0] / results.metadata.duration_seconds) * 100}%`,
                  width: `${((timeWindow[1] - timeWindow[0]) / results.metadata.duration_seconds) * 100}%`,
                }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-600 mt-1">
              <span>0s</span>
              <span className="font-semibold">
                {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
              </span>
              <span>{results.metadata.duration_seconds.toFixed(1)}s</span>
            </div>
          </div>
        </div>

        {/* Multi-channel view */}
        <div ref={channelViewRef} className={`flex-1 flex overflow-hidden bg-white rounded-lg shadow-sm ${isFullscreen ? "m-0" : "m-4"}`}>
          {/* Channel selector sidebar */}
          {!isFullscreen && (
            <ChannelSelector
              channels={results.metadata.channels}
              selectedChannels={selectedChannels}
              onChannelToggle={(channel) => {
                setSelectedChannels((prev) => (prev.includes(channel) ? prev.filter((ch) => ch !== channel) : [...prev, channel]));
              }}
              onSelectAll={() => setSelectedChannels(results.metadata.channels)}
              onClearAll={() => setSelectedChannels([])}
              hfoCountByChannel={calculateHfoCountByChannel(
                results.hfo_events,
                results.metadata.channels
              )}
            />
          )}

          {/* Channel grid display */}
          <div className="flex-1 flex flex-col">
            <div className="flex-shrink-0 h-14 bg-gray-50 border-b border-gray-200 px-4 flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-sm font-semibold text-gray-900">Channel View</h2>
                <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                  {selectedChannels.length}/{results.metadata.channels.length} channels
                </span>
                {hfoStatsByType && (
                  <span className="text-xs text-gray-600 bg-white px-2 py-1 rounded">
                    {filteredHfoEvents.length} of {hfoStatsByType.total} HFOs
                  </span>
                )}
              </div>
              <div className="flex items-center gap-2">
                {isFullscreen && (
                  <button
                    onClick={() => document.exitFullscreen()}
                    className="px-3 py-1 bg-gray-700 text-white text-xs rounded hover:bg-gray-600 transition-colors"
                  >
                    Exit Fullscreen (F)
                  </button>
                )}
              </div>
            </div>

            <div className="flex-1 overflow-auto">
              <ChannelGrid
                channelData={results.channel_data || {}}
                visibleChannels={selectedChannels}
                timeWindow={timeWindow}
                samplingRate={results.sampling_info?.effective_sampling_rate || results.metadata.sampling_rate}
                hfoEvents={filteredHfoEvents}
                showHFOMarkers={true}
                gain={gain}
                samplingInfo={results.sampling_info}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};