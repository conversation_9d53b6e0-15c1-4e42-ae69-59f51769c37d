import React, { useState } from "react";
import { Button } from "@/components/ui/button";

// Form section components
import { FrequencySettings } from "@/components/forms/FrequencySettings";
import { ThresholdSettings } from "@/components/forms/ThresholdSettings";
import { MontageSettings } from "@/components/forms/MontageSettings";
import { TimeSegmentSettings } from "@/components/forms/TimeSegmentSettings";

export interface AnalysisParameters {
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  thresholds: {
    amplitude_1: number; // StdDev above energy signal
    amplitude_2: number; // StdDev above baseline
    peaks_1: number; // Min peaks in HFO
    peaks_2: number; // Min peaks above threshold
    duration: number; // Min HFO length (ms)
    temporal_sync: number; // Min separation (ms)
    spatial_sync: number; // Channel delay (ms)
  };
  montage: {
    type: "bipolar" | "average" | "referential";
    reference?: string;
  };
  analysis_start?: number;
  analysis_end?: number;
}

interface AnalysisParametersFormProps {
  onParametersChange: (params: AnalysisParameters) => void;
  onSubmit: (params: AnalysisParameters) => void;
  isSubmitting?: boolean;
  channels?: string[];
}

export const AnalysisParametersForm: React.FC<AnalysisParametersFormProps> = ({
  onParametersChange,
  onSubmit,
  isSubmitting = false,
  channels = [],
}) => {
  // Default parameters (aligned with original backend)
  const [parameters, setParameters] = useState<AnalysisParameters>({
    frequency: {
      low_cutoff: 50,
      high_cutoff: 300,
    },
    thresholds: {
      amplitude_1: 2,
      amplitude_2: 2,
      peaks_1: 6, // Min peaks
      peaks_2: 3, // Min peaks above baseline
      duration: 10, // Min duration (ms)
      temporal_sync: 10, // Min separation (ms)
      spatial_sync: 10, // Channel delay (ms) across channels
    },
    montage: {
      type: "bipolar",
    },
    analysis_start: undefined,
    analysis_end: undefined,
  });

  const updateParameter = (section: string, field: string, value: string | number) => {
    // Handle top-level optional fields
    if (section === "root") {
      const updated = {
        ...parameters,
        [field]: value === "" ? undefined : value,
      };
      setParameters(updated);
      onParametersChange(updated);
    } else {
      const sectionData = parameters[section as keyof AnalysisParameters];
      if (typeof sectionData === "object" && sectionData !== null) {
        const updated = {
          ...parameters,
          [section]: {
            ...sectionData,
            [field]: value,
          },
        };
        setParameters(updated);
        onParametersChange(updated);
      }
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(parameters);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Frequency configuration */}
      <FrequencySettings
        frequency={parameters.frequency}
        onUpdate={(field, value) => updateParameter("frequency", field, value)}
      />

      {/* Detection thresholds */}
      <ThresholdSettings
        thresholds={parameters.thresholds}
        onUpdate={(field, value) => updateParameter("thresholds", field, value)}
      />

      {/* Montage configuration */}
      <MontageSettings
        montage={parameters.montage}
        channels={channels}
        onUpdate={(field, value) => updateParameter("montage", field, value)}
      />

      {/* Time segment */}
      <TimeSegmentSettings
        analysisStart={parameters.analysis_start}
        analysisEnd={parameters.analysis_end}
        onUpdate={(field, value) => updateParameter("root", field, value)}
      />

      {/* Submit button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "Processing..." : "Start Analysis"}
        </Button>
      </div>
    </form>
  );
};