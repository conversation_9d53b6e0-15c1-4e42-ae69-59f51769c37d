import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardD<PERSON>cription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Settings } from "lucide-react";
import { ThresholdSettingsForm } from "./forms/ThresholdSettings";
import { FrequencySettingsForm } from "./forms/FrequencySettings";
import { ChannelSelection } from "./forms/ChannelSelection";
import { TimeSegmentSelector } from "./forms/TimeSegmentSelector";
import { MontageSelector } from "./forms/MontageSelector";
import { useFormValidation } from "@/hooks/useFormValidation";
import { ErrorMessage } from "@/components/ui/feedback/ErrorMessage";

export interface AnalysisParameters {
  frequency: {
    low_cutoff: number;
    high_cutoff: number;
  };
  thresholds: {
    amplitude_1: number;
    amplitude_2: number;
    peaks_1: number;
    peaks_2: number;
    duration: number;
    temporal_sync: number;
    spatial_sync: number;
  };
  montage: {
    type: "bipolar" | "average" | "referential";
    reference?: string;
  };
  channelSelection?: {
    selectedChannels: string[];
  };
  timeSegment?: {
    mode: "entire_file" | "start_end_times" | "start_duration";
    startTime?: number;
    endTime?: number;
    duration?: number;
  };
  analysis_start?: number;
  analysis_end?: number;
}

interface EnhancedAnalysisParametersFormProps {
  onParametersChange: (params: AnalysisParameters) => void;
  onSubmit: (params: AnalysisParameters) => void;
  isSubmitting?: boolean;
  channels?: string[];
  fileInfo?: {
    filename: string;
    samplingRate: number;
    duration: number;
    startDate?: string;
    startTime?: string;
  };
}

export const EnhancedAnalysisParametersForm: React.FC<EnhancedAnalysisParametersFormProps> = ({
  onParametersChange,
  onSubmit,
  isSubmitting = false,
  channels = [],
  fileInfo,
}) => {
  const { errors, validateParameters } = useFormValidation();

  // Default parameters
  const [parameters, setParameters] = useState<AnalysisParameters>({
    frequency: {
      low_cutoff: 50,
      high_cutoff: 300,
    },
    thresholds: {
      amplitude_1: 2,
      amplitude_2: 2,
      peaks_1: 6,
      peaks_2: 3,
      duration: 10,
      temporal_sync: 10,
      spatial_sync: 10,
    },
    montage: {
      type: "bipolar",
    },
    channelSelection: {
      selectedChannels: channels,
    },
    timeSegment: {
      mode: "entire_file",
    },
  });

  const updateParameter = (section: string, field: string, value: string | number | boolean | string[] | undefined) => {
    const sectionData = parameters[section as keyof AnalysisParameters];
    if (typeof sectionData === 'object' && sectionData !== null) {
      const updated = {
        ...parameters,
        [section]: {
          ...sectionData,
          [field]: value,
        },
      };
      setParameters(updated);
      onParametersChange(updated);
    }
  };

  const handleChannelToggle = (channel: string) => {
    const currentSelection = parameters.channelSelection?.selectedChannels || [];
    const updated = currentSelection.includes(channel)
      ? currentSelection.filter((ch) => ch !== channel)
      : [...currentSelection, channel];

    const newParams = {
      ...parameters,
      channelSelection: {
        selectedChannels: updated,
      },
    };
    setParameters(newParams);
    onParametersChange(newParams);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Map time segment to analysis_start/end for backward compatibility
    if (parameters.timeSegment) {
      if (parameters.timeSegment.mode === "start_end_times") {
        parameters.analysis_start = parameters.timeSegment.startTime;
        parameters.analysis_end = parameters.timeSegment.endTime;
      } else if (parameters.timeSegment.mode === "start_duration") {
        parameters.analysis_start = parameters.timeSegment.startTime;
        parameters.analysis_end = (parameters.timeSegment.startTime || 0) + (parameters.timeSegment.duration || 0);
      }
    }

    if (validateParameters(parameters)) {
      onSubmit(parameters);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* File Information */}
      {fileInfo && (
        <Card className="bg-gray-50">
          <CardHeader>
            <CardTitle className="text-lg">File Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Filename:</span>
                <p className="font-medium">{fileInfo.filename}</p>
              </div>
              <div>
                <span className="text-gray-600">Sampling Rate:</span>
                <p className="font-medium">{fileInfo.samplingRate} Hz</p>
              </div>
              <div>
                <span className="text-gray-600">Duration:</span>
                <p className="font-medium">{fileInfo.duration.toFixed(2)} seconds</p>
              </div>
              {fileInfo.startDate && (
                <div>
                  <span className="text-gray-600">Recording Date:</span>
                  <p className="font-medium">{fileInfo.startDate} {fileInfo.startTime}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Parameters */}
      <Card>
        <CardHeader>
          <CardTitle>
            <Settings className="inline-block mr-2 h-5 w-5" />
            Analysis Parameters
          </CardTitle>
          <CardDescription>
            Configure the parameters for HFO detection analysis
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Frequency Settings */}
          <FrequencySettingsForm
            frequency={parameters.frequency}
            onUpdate={(field, value) => updateParameter("frequency", field, value)}
          />

          {/* Threshold Settings */}
          <ThresholdSettingsForm
            thresholds={parameters.thresholds}
            onUpdate={(field, value) => updateParameter("thresholds", field, value)}
          />

          {/* Montage Configuration */}
          <MontageSelector
            montage={parameters.montage}
            onUpdate={(field, value) => updateParameter("montage", field, value)}
            channels={channels}
          />

          {/* Channel Selection */}
          {channels.length > 0 && (
            <ChannelSelection
              channels={channels}
              selectedChannels={parameters.channelSelection?.selectedChannels || []}
              onChannelToggle={handleChannelToggle}
              onSelectAll={() => {
                const newParams = {
                  ...parameters,
                  channelSelection: { selectedChannels: channels },
                };
                setParameters(newParams);
                onParametersChange(newParams);
              }}
              onDeselectAll={() => {
                const newParams = {
                  ...parameters,
                  channelSelection: { selectedChannels: [] },
                };
                setParameters(newParams);
                onParametersChange(newParams);
              }}
            />
          )}

          {/* Time Segment */}
          <TimeSegmentSelector
            timeSegment={parameters.timeSegment!}
            onUpdate={(field, value) => updateParameter("timeSegment", field, value)}
            fileDuration={fileInfo?.duration}
          />

          {/* Error Messages */}
          {Object.keys(errors).length > 0 && (
            <ErrorMessage
              error="Please correct the errors above before submitting"
              variant="inline"
            />
          )}

          {/* Submit Button */}
          <div className="flex justify-end pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || Object.keys(errors).length > 0}
              className="min-w-[150px]"
            >
              {isSubmitting ? (
                <>
                  <span className="animate-spin mr-2">⏳</span>
                  Processing...
                </>
              ) : (
                "Start Analysis"
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </form>
  );
};