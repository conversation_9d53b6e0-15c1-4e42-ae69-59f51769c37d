import React from 'react';

interface KeyboardShortcutsHintProps {
  className?: string;
}

export const KeyboardShortcutsHint: React.FC<KeyboardShortcutsHintProps> = ({
  className = '',
}) => {
  const shortcuts = [
    { key: 'A/D', action: 'Zoom' },
    { key: 'O/P', action: 'Navigate' },
    { key: 'F', action: 'Fullscreen' },
    { key: 'Shift+Scroll', action: 'Horizontal scroll' },
  ];

  return (
    <div className={`text-xs text-gray-500 flex items-center justify-center gap-4 pt-2 border-t ${className}`}>
      <span>Keyboard Shortcuts:</span>
      {shortcuts.map((shortcut, index) => (
        <span key={index}>
          {shortcut.key} - {shortcut.action}
        </span>
      ))}
    </div>
  );
};