import React from 'react';
import { Activity } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface GainControlProps {
  gain: number;
  onGainChange: (gain: number) => void;
  gainOptions?: { value: number; label: string }[];
}

export const GainControl: React.FC<GainControlProps> = ({
  gain,
  onGainChange,
  gainOptions = [
    { value: 5, label: '5 μV/div' },
    { value: 10, label: '10 μV/div' },
    { value: 20, label: '20 μV/div' },
    { value: 50, label: '50 μV/div' },
    { value: 100, label: '100 μV/div' },
  ],
}) => {
  return (
    <div className="flex items-center gap-2">
      <Activity className="h-4 w-4 text-gray-600" />
      <span className="text-sm text-gray-600">Gain:</span>
      <Select
        value={gain.toString()}
        onValueChange={(value: string) => onGainChange(Number(value))}
      >
        <SelectTrigger className="w-24 h-8">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {gainOptions.map(option => (
            <SelectItem key={option.value} value={option.value.toString()}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};