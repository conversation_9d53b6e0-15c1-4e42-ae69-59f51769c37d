import React from 'react';
import { Download, Maximize2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ActionButtonsProps {
  onExport?: () => void;
  onFullscreen?: () => void;
  isFullscreen?: boolean;
  showKeyboardHints?: boolean;
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
  onExport,
  onFullscreen,
  isFullscreen = false,
  showKeyboardHints = true,
}) => {
  return (
    <div className="flex items-center gap-2">
      {onExport && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              className="h-8"
            >
              <Download className="h-4 w-4" />
              <span className="ml-1">Export</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Export plot as image</p>
          </TooltipContent>
        </Tooltip>
      )}

      {onFullscreen && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={onFullscreen}
              className="h-8"
            >
              <Maximize2 className="h-4 w-4" />
              <span className="ml-1">{isFullscreen ? 'Exit' : 'Fullscreen'}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Toggle Fullscreen {showKeyboardHints && <span className="text-xs">(F)</span>}</p>
          </TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};