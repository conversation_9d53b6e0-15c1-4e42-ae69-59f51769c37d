import React from 'react';
import { <PERSON>, EyeOff, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface DisplayControlsProps {
  showHFOMarkers: boolean;
  onToggleHFOMarkers: () => void;
  showThresholds: boolean;
  onToggleThresholds: () => void;
}

export const DisplayControls: React.FC<DisplayControlsProps> = ({
  showHFOMarkers,
  onToggleHFOMarkers,
  showThresholds,
  onToggleThresholds,
}) => {
  return (
    <div className="flex items-center gap-2">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={showHFOMarkers ? "default" : "outline"}
            size="sm"
            onClick={onToggleHFOMarkers}
            className="h-8"
          >
            {showHFOMarkers ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            <span className="ml-1">HFOs</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{showHFOMarkers ? 'Hide' : 'Show'} HFO Markers</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={showThresholds ? "default" : "outline"}
            size="sm"
            onClick={onToggleThresholds}
            className="h-8"
          >
            <Settings className="h-4 w-4" />
            <span className="ml-1">Thresholds</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{showThresholds ? 'Hide' : 'Show'} Detection Thresholds</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};