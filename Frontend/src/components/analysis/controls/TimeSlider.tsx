import React from 'react';
import { Slider } from '@/components/ui/slider';

interface TimeSliderProps {
  timeWindow: [number, number];
  duration: number;
  onTimeWindowChange: (window: [number, number]) => void;
}

export const TimeSlider: React.FC<TimeSliderProps> = ({
  timeWindow,
  duration,
  onTimeWindowChange,
}) => {
  const windowSize = timeWindow[1] - timeWindow[0];

  const handleSliderChange = (value: number[]) => {
    const newStart = value[0];
    onTimeWindowChange([newStart, Math.min(newStart + windowSize, duration)]);
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Time Range:</span>
        <span className="font-mono font-medium">
          {timeWindow[0].toFixed(1)}s - {timeWindow[1].toFixed(1)}s
        </span>
      </div>
      <div className="flex items-center gap-4">
        <span className="text-xs text-gray-500 w-10">0s</span>
        <Slider
          value={[timeWindow[0]]}
          onValueChange={handleSliderChange}
          max={Math.max(0, duration - windowSize)}
          step={0.1}
          className="flex-1"
        />
        <span className="text-xs text-gray-500 w-10">{duration.toFixed(0)}s</span>
      </div>

      {/* Position indicator */}
      <div className="relative h-2 bg-gray-200 rounded">
        <div
          className="absolute h-full bg-blue-600 rounded transition-all duration-200"
          style={{
            left: `${(timeWindow[0] / duration) * 100}%`,
            width: `${((timeWindow[1] - timeWindow[0]) / duration) * 100}%`,
          }}
        />
      </div>
    </div>
  );
};