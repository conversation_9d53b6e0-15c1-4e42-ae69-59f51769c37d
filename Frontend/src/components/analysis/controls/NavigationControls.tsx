import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface NavigationControlsProps {
  onNavigate: (direction: 'prev' | 'next') => void;
  canNavigatePrev: boolean;
  canNavigateNext: boolean;
  showKeyboardHints?: boolean;
}

export const NavigationControls: React.FC<NavigationControlsProps> = ({
  onNavigate,
  canNavigatePrev,
  canNavigateNext,
  showKeyboardHints = true,
}) => {
  return (
    <div className="flex items-center gap-1 border rounded-md p-1">
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onNavigate('prev')}
            disabled={!canNavigatePrev}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Previous {showKeyboardHints && <span className="text-xs">(O)</span>}</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onNavigate('next')}
            disabled={!canNavigateNext}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Next {showKeyboardHints && <span className="text-xs">(P)</span>}</p>
        </TooltipContent>
      </Tooltip>
    </div>
  );
};