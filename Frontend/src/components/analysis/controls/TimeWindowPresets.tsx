import React from 'react';
import { Button } from '@/components/ui/button';

interface TimeWindowPresetsProps {
  timeWindow: [number, number];
  duration: number;
  onTimeWindowChange: (window: [number, number]) => void;
  presets?: number[];
}

export const TimeWindowPresets: React.FC<TimeWindowPresetsProps> = ({
  timeWindow,
  duration,
  onTimeWindowChange,
  presets = [1, 5, 10, 30],
}) => {
  const windowSize = timeWindow[1] - timeWindow[0];

  const handlePresetClick = (preset: number) => {
    const center = (timeWindow[0] + timeWindow[1]) / 2;
    const newStart = Math.max(0, center - preset / 2);
    const newEnd = Math.min(duration, center + preset / 2);

    // Adjust if we hit boundaries
    if (newEnd - newStart < preset) {
      if (newStart === 0) {
        onTimeWindowChange([0, Math.min(preset, duration)]);
      } else {
        onTimeWindowChange([Math.max(0, duration - preset), duration]);
      }
    } else {
      onTimeWindowChange([newStart, newEnd]);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-gray-600">Window:</span>
      <div className="flex gap-1">
        {presets.map((preset) => (
          <Button
            key={preset}
            variant={Math.abs(windowSize - preset) < 0.1 ? "default" : "outline"}
            size="sm"
            onClick={() => handlePresetClick(preset)}
            className="h-8 px-2"
          >
            {preset}s
          </Button>
        ))}
      </div>
    </div>
  );
};