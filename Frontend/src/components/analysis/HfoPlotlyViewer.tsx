import React, { useMemo, useState, useRef, useCallback } from "react";
import Plot from "react-plotly.js";
import * as Plotly from "plotly.js";
import { Card } from "@/components/ui/card";
import { PlotControlPanel } from "./PlotControlPanel";
import { ChannelSelectionPanel } from "./ChannelSelectionPanel";
import { HfoStatistics } from "./HfoStatistics";
import { useTimeWindow } from "@/hooks/useTimeWindow";
import { useFullscreen } from "@/hooks/useFullscreen";
import { useKeyboardShortcuts, COMMON_SHORTCUTS } from "@/hooks/useKeyboardShortcuts";
import { useDownloadExport } from "@/hooks/useDownloadExport";
import { decimateData, decimateTimeAxis, getDecimationConfig } from "@/utils/visualization/dataDecimation";
import { generateChannelTrace, generateHFOMarkerTraces, generateThresholdTraces } from "@/utils/visualization/hfoTraceGenerator";
import { createPlotLayout, createPlotConfig, calculateChannelOffset } from "@/config/plotConfig";
import type { HFOEvent } from "@/types/hfo";

interface HfoPlotlyViewerProps {
  channelData: Record<string, number[]>;
  hfoEvents: HFOEvent[];
  samplingRate: number;
  duration: number;
  channelLabels: string[];
  metadata?: {
    filename?: string;
    montage?: string;
    frequency_band?: string;
  };
}

export const HfoPlotlyViewer: React.FC<HfoPlotlyViewerProps> = React.memo(
  ({ channelData, hfoEvents, samplingRate, duration, channelLabels, metadata }) => {
    // ─── REFS ───
    const containerRef = useRef<HTMLDivElement | null>(null);

    // ─── STATE ───
    const [gain, setGain] = useState(20);
    const [showHFOMarkers, setShowHFOMarkers] = useState(true);
    const [showThresholds, setShowThresholds] = useState(false);
    const [selectedChannels, setSelectedChannels] = useState<string[]>(channelLabels);
    const [isPanelCollapsed, setIsPanelCollapsed] = useState(false);

    // ─── CUSTOM HOOKS ───
    const timeWindowControls = useTimeWindow({
      duration,
      initialWindow: [0, Math.min(5, duration)]
    });
    const { timeWindow, zoomWindow, panWindow, resetWindow } = timeWindowControls;

    const { isFullscreen, toggleFullscreen } = useFullscreen(containerRef);
    const { downloadImage } = useDownloadExport();

    // ─── CALCULATIONS ───
    const hfoCountPerChannel = useMemo(() => {
      const counts: Record<string, number> = {};
      channelLabels.forEach((ch) => {
        counts[ch] = hfoEvents.filter((e) => e.channel === ch).length;
      });
      return counts;
    }, [hfoEvents, channelLabels]);

    const channelOffset = calculateChannelOffset(selectedChannels.length, gain);

    // ─── GENERATE PLOT DATA ───
    const plotData = useMemo(() => {
      const traces: Plotly.Data[] = [];
      const windowDuration = timeWindow[1] - timeWindow[0];
      const { maxPointsPerChannel } = getDecimationConfig(windowDuration);

      selectedChannels.forEach((channel, index) => {
        const data = channelData[channel];
        if (!data || data.length === 0) return;

        // ─── TIME AXIS CREATION ───
        const timeAxis = Array.from({ length: data.length }, (_, i) => i / samplingRate);
        const startIdx = Math.floor(timeWindow[0] * samplingRate);
        const endIdx = Math.ceil(timeWindow[1] * samplingRate);
        let windowedTime = timeAxis.slice(startIdx, endIdx);
        let windowedData = data.slice(startIdx, endIdx);

        // ─── APPLY DECIMATION ───
        if (windowedData.length > maxPointsPerChannel) {
          const decimationFactor = Math.ceil(windowedData.length / maxPointsPerChannel);
          windowedData = decimateData(windowedData, maxPointsPerChannel);
          windowedTime = decimateTimeAxis(windowedTime, decimationFactor);
        }

        // ─── MAIN CHANNEL TRACE ───
        traces.push(generateChannelTrace(
          channel,
          windowedData,
          windowedTime,
          index,
          gain,
          channelOffset
        ));

        // ─── HFO MARKERS ───
        if (showHFOMarkers) {
          traces.push(...generateHFOMarkerTraces(
            hfoEvents,
            channel,
            index,
            timeWindow,
            channelOffset
          ));
        }

        // ─── THRESHOLD LINES ───
        if (showThresholds) {
          traces.push(...generateThresholdTraces(
            channel,
            index,
            windowedTime,
            channelOffset
          ));
        }
      });

      return traces;
    }, [channelData, selectedChannels, hfoEvents, timeWindow, samplingRate, showHFOMarkers, showThresholds, gain, channelOffset]);

    // ─── PLOT CONFIGURATION ───
    const layout = createPlotLayout({
      timeWindow,
      selectedChannels,
      channelOffset,
      metadata
    });

    const config = createPlotConfig(metadata?.filename);

    // ─── HANDLERS ───
    const handleReset = useCallback(() => {
      resetWindow();
      setGain(20);
      setShowHFOMarkers(true);
      setShowThresholds(false);
      setSelectedChannels(channelLabels);
    }, [resetWindow, channelLabels]);

    const handleNavigate = useCallback(
      (direction: "prev" | "next") => {
        panWindow(direction === "prev" ? -1 : 1);
      },
      [panWindow]
    );

    const handleChannelToggle = (channel: string) => {
      setSelectedChannels((prev) =>
        prev.includes(channel)
          ? prev.filter((ch) => ch !== channel)
          : [...prev, channel]
      );
    };

    const handleSelectAll = () => setSelectedChannels(channelLabels);
    const handleClearAll = () => setSelectedChannels([]);

    const handleExport = useCallback(async () => {
      const plotElement = document.querySelector(".js-plotly-plot") as HTMLElement;
      if (plotElement) {
        await downloadImage(plotElement, {
          filename: `hfo_analysis_${metadata?.filename?.replace(".edf", "") || "export"}_${new Date().toISOString().split("T")[0]}`
        });
      }
    }, [downloadImage, metadata]);

    // ─── KEYBOARD SHORTCUTS ───
    useKeyboardShortcuts([
      { key: 'a', handler: () => zoomWindow('in') },
      { key: 'd', handler: () => zoomWindow('out') },
      { key: 'o', handler: () => handleNavigate('prev') },
      { key: 'p', handler: () => handleNavigate('next') },
      { key: COMMON_SHORTCUTS.FULLSCREEN, handler: () => toggleFullscreen() },
      { key: 'r', handler: handleReset }
    ]);

    return (
      <div className="space-y-4" ref={containerRef}>
        {/* ─── CONTROL PANEL ─── */}
        <PlotControlPanel
          timeWindow={timeWindow}
          onTimeWindowChange={timeWindowControls.setTimeWindow}
          duration={duration}
          onZoomIn={() => zoomWindow('in')}
          onZoomOut={() => zoomWindow('out')}
          onReset={handleReset}
          gain={gain}
          onGainChange={setGain}
          showHFOMarkers={showHFOMarkers}
          onToggleHFOMarkers={() => setShowHFOMarkers(!showHFOMarkers)}
          showThresholds={showThresholds}
          onToggleThresholds={() => setShowThresholds(!showThresholds)}
          onNavigate={handleNavigate}
          onExport={handleExport}
          onFullscreen={toggleFullscreen}
          isFullscreen={isFullscreen}
          showKeyboardHints={true}
        />

        {/* ─── MAIN PLOT WITH CHANNEL SELECTION ─── */}
        <div className="flex gap-4 h-[80vh] overflow-hidden">
          <ChannelSelectionPanel
            channels={channelLabels}
            selectedChannels={selectedChannels}
            onChannelToggle={handleChannelToggle}
            onSelectAll={handleSelectAll}
            onClearAll={handleClearAll}
            hfoCountPerChannel={hfoCountPerChannel}
            isCollapsed={isPanelCollapsed}
            onToggleCollapse={() => setIsPanelCollapsed(!isPanelCollapsed)}
          />

          <Card className="flex-1 p-4 overflow-auto">
            <div style={{ minHeight: `${Math.max(600, selectedChannels.length * 80 + 150)}px` }}>
              <Plot
                data={plotData}
                layout={layout}
                config={config}
                useResizeHandler
                style={{ width: "100%", height: "100%" }}
              />
            </div>
          </Card>
        </div>

        {/* ─── STATISTICS ─── */}
        <HfoStatistics
          hfoEvents={hfoEvents}
          timeWindow={timeWindow}
          selectedChannels={selectedChannels}
          samplingRate={samplingRate}
        />
      </div>
    );
  }
);
