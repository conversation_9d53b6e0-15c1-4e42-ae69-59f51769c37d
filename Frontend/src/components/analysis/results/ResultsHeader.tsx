import React from 'react';
import { Download } from 'lucide-react';

interface ResultsHeaderProps {
  filename: string;
  onDownload: (format: string) => void;
  onClose?: () => void;
}

export const ResultsHeader: React.FC<ResultsHeaderProps> = ({
  filename,
  onDownload,
  onClose,
}) => {
  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-xl font-bold text-gray-900">HFO Analysis Results</h1>
          <p className="text-sm text-gray-600">{filename}</p>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => onDownload("csv")}
            className="px-3 py-1 bg-black text-white text-xs rounded hover:bg-gray-800 transition-colors flex items-center gap-1"
            title="Download HFO events"
          >
            <Download className="w-3 h-3" />
            Export
          </button>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl"
              title="Close"
            >
              ×
            </button>
          )}
        </div>
      </div>
    </header>
  );
};