import React, { useEffect, useState, useCallback } from "react";
import { Download, Trash2, FileIcon, RefreshCw, CheckSquare, Square, Loader2, Settings } from "lucide-react";
import { useFileOperations } from "@/hooks/useFileOperations";
import { useFileActions } from "@/hooks/useFileActions";
import { useFileSelection } from "@/hooks/useFileSelection";
import { useFileMetadata } from "@/hooks/useFileMetadata";
import { formatFileSize, formatDate } from "@/utils/file";
import { buildAnalysisApiParams, buildBatchAnalysisPayload } from "@/utils/api/analysisParameterBuilder";
import { useToast } from "@/hooks/useToast";
import { DataTable, type TableColumn } from "./tables/DataTable";
import Toast from "./Toast";
import ConfirmDialog from "./ConfirmDialog";
import { useConfirmDialog } from "@/hooks/useConfirmDialog";
import { apiClient } from "@/services/api";
import type { FileInfo } from "@/types/api";
import type { AnalysisParameters } from "./analysis/EnhancedAnalysisParametersForm";
import { FileProcessingModal } from "./files/FileProcessingModal";

interface FileManagerProps {
  refreshTrigger?: number;
}

export const FileManager: React.FC<FileManagerProps> = ({ refreshTrigger }) => {
  // ─── HOOKS ───
  const { toast, showToast, hideToast } = useToast();
  const { dialogState, showConfirmDialog } = useConfirmDialog();
  const { files, loading, error, fetchFiles } = useFileOperations();
  const { handleDownload, handleDelete, isOperationInProgress } = useFileActions({
    onDeleteSuccess: fetchFiles,
    showConfirmDialog,
  });
  const fileSelection = useFileSelection();
  const { selectedFiles, toggleFile, selectAll, clearAll } = fileSelection;
  const { metadata: fileMetadata, loading: fetchingMetadata, fetchMetadata } = useFileMetadata();

  // ─── STATE ───
  const [processing, setProcessing] = useState(false);
  const [parametersModalOpen, setParametersModalOpen] = useState(false);
  const [modalStep, setModalStep] = useState<"details" | "parameters">("details");
  const [useEnhancedForm, setUseEnhancedForm] = useState(false);
  const [activeFileKey, setActiveFileKey] = useState<string | null>(null);

  // ─── EFFECTS ───
  useEffect(() => {
    fetchFiles();
  }, [refreshTrigger, fetchFiles]);

  // ─── HANDLERS ───
  const handleSelectAll = () => {
    if (selectedFiles.length === files.length) {
      clearAll();
    } else {
      selectAll(files.map((f) => f.key));
    }
  };

  const handleActiveFileChange = useCallback(
    async (fileKey: string) => {
      setActiveFileKey(fileKey);
      await fetchMetadata(fileKey);
    },
    [fetchMetadata]
  );

  const handleProcessFiles = (enhanced = false) => {
    if (selectedFiles.length === 0) {
      showToast("Please select at least one file to process", "error");
      return;
    }

    if (enhanced && selectedFiles.length > 0) {
      const primaryFile = activeFileKey && selectedFiles.includes(activeFileKey) ? activeFileKey : selectedFiles[0];
      void handleActiveFileChange(primaryFile);
    }

    setUseEnhancedForm(enhanced);
    setModalStep(enhanced ? "details" : "parameters");
    setParametersModalOpen(true);
  };

  // ─── HELPERS ───
  const getChannelsForFile = useCallback((fileKey: string) => {
    if (useEnhancedForm && activeFileKey === fileKey) {
      return fileMetadata?.channels ?? [];
    }

    const file = files.find((f) => f.key === fileKey);
    if (file && 'channels' in file) {
      return (file as unknown as { channels?: string[] }).channels ?? [];
    }

    return fileMetadata?.channels ?? [];
  }, [useEnhancedForm, activeFileKey, fileMetadata, files]);

  const handleSubmitWithParameters = async (params: AnalysisParameters) => {
    setParametersModalOpen(false);
    setModalStep("details");
    setProcessing(true);

    try {
      if (useEnhancedForm) {
        if (!activeFileKey) {
          showToast("Select a file to configure before submitting", "error");
          return;
        }

        const channels = getChannelsForFile(activeFileKey);
        const payload = {
          files: [
            {
              file_key: activeFileKey,
              parameters: buildAnalysisApiParams(params, channels),
            },
          ],
        };

        await apiClient.post("/analysis/batch", payload);

        const processedFile = files.find((file) => file.key === activeFileKey);
        if (processedFile) {
          showToast(`Processing "${processedFile.filename}" with custom parameters. You'll receive an email when complete.`, "success");
        } else {
          showToast("Processing file with custom parameters. You'll receive an email when complete.", "success");
        }

        fileSelection.deselectFile(activeFileKey);
        setActiveFileKey(null);
      } else {
        if (selectedFiles.length === 0) {
          showToast("Please select at least one file to process", "error");
          return;
        }

        const payload = buildBatchAnalysisPayload(
          selectedFiles,
          params,
          getChannelsForFile
        );

        await apiClient.post("/analysis/batch", payload);

        showToast(`Processing ${selectedFiles.length} file(s) with custom parameters. You'll receive an email when complete.`, "success");

        clearAll();
      }
    } catch {
      showToast("Failed to submit files for processing", "error");
    } finally {
      setProcessing(false);
    }
  };

  const columns: TableColumn<FileInfo>[] = [
    {
      key: "select",
      header: (
        <button
          onClick={handleSelectAll}
          className="p-1 hover:bg-gray-100 rounded"
          title={selectedFiles.length === files.length ? "Deselect All" : "Select All"}
        >
          {selectedFiles.length === files.length ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
      accessor: (file) => (
        <button onClick={() => toggleFile(file.key)} className="p-1 hover:bg-gray-100 rounded">
          {selectedFiles.includes(file.key) ? <CheckSquare className="w-5 h-5 text-blue-600" /> : <Square className="w-5 h-5 text-gray-400" />}
        </button>
      ),
    },
    {
      key: "filename",
      header: "File Name",
      accessor: (file) => (
        <div className="flex items-center">
          <FileIcon className="w-5 h-5 mr-2 text-gray-400" />
          <span className="text-sm font-medium text-gray-900">{file.filename}</span>
        </div>
      ),
    },
    {
      key: "size",
      header: "Size",
      accessor: (file) => <span className="text-sm text-gray-500">{formatFileSize(file.size)}</span>,
    },
    {
      key: "modified",
      header: "Modified",
      accessor: (file) => <span className="text-sm text-gray-500">{formatDate(file.last_modified)}</span>,
    },
    {
      key: "actions",
      header: "Actions",
      className: "text-right",
      accessor: (file) => (
        <div className="flex justify-end gap-2">
          <button
            onClick={() => handleDownload(file.key)}
            disabled={isOperationInProgress("download", file.key)}
            className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
            title="Download"
          >
            {isOperationInProgress("download", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Download className="w-4 h-4" />}
          </button>
          <button
            onClick={() => handleDelete(file.key, file.filename)}
            disabled={isOperationInProgress("delete", file.key)}
            className="text-red-600 hover:text-red-900 disabled:opacity-50"
            title="Delete"
          >
            {isOperationInProgress("delete", file.key) ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Trash2 className="w-4 h-4" />}
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      {toast.isOpen && <Toast message={toast.message} type={toast.type} onClose={hideToast} />}
      <ConfirmDialog {...dialogState} />

      <div className="space-y-4">
        {/* Process Files Button */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-between bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center">
              <CheckSquare className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-gray-900">{selectedFiles.length} file(s) selected</span>
            </div>
            <div className="flex gap-2">
              {processing ? (
                <>
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  Processing...
                </>
              ) : fetchingMetadata ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Loading EDF data...
                </>
              ) : (
                <button
                  onClick={() => handleProcessFiles(true)}
                  disabled={processing || fetchingMetadata || selectedFiles.length === 0}
                  className="flex items-center px-4 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Configure & Analyze
                </button>
              )}
            </div>
          </div>
        )}

        <DataTable
          title={`Files Uploaded (Ready to be Analyzed) (${files.length})`}
          columns={columns}
          data={files}
          loading={loading}
          error={error}
          emptyMessage="No uploaded files available"
          onRefresh={fetchFiles}
          getRowKey={(file) => file.key}
        />
      </div>

      {/* ─── PROCESSING MODAL ─── */}
      <FileProcessingModal
        open={parametersModalOpen}
        onOpenChange={setParametersModalOpen}
        modalStep={modalStep}
        onStepChange={setModalStep}
        useEnhancedForm={useEnhancedForm}
        fetchingMetadata={fetchingMetadata}
        files={files}
        selectedFiles={selectedFiles}
        activeFileKey={activeFileKey}
        onFileChange={handleActiveFileChange}
        onSubmit={handleSubmitWithParameters}
        processing={processing}
        fileMetadata={fileMetadata}
      />

      {/* Loading Overlay */}
      {processing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 flex flex-col items-center gap-4 shadow-xl">
            <Loader2 className="w-12 h-12 animate-spin text-blue-600" />
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Submitting Analysis Request</h3>
              <p className="text-sm text-gray-600">
                Processing {selectedFiles.length} file{selectedFiles.length > 1 ? "s" : ""}...
              </p>
              <p className="text-xs text-gray-500 mt-2">This may take a few moments. You'll receive an email when complete.</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
