import { useEffect, useCallback } from 'react';

export interface ShortcutHandler {
  key: string | string[];
  handler: (event: KeyboardEvent) => void;
  preventDefault?: boolean;
  ctrl?: boolean;
  meta?: boolean;
  shift?: boolean;
  alt?: boolean;
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean;
  ignoreInputElements?: boolean;
}

export const useKeyboardShortcuts = (
  shortcuts: ShortcutHandler[],
  options: UseKeyboardShortcutsOptions = {}
) => {
  const {
    enabled = true,
    ignoreInputElements = true
  } = options;

  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // ─── IGNORE INPUT ELEMENTS ───
    if (ignoreInputElements &&
        (event.target instanceof HTMLInputElement ||
         event.target instanceof HTMLTextAreaElement)) {
      return;
    }

    shortcuts.forEach(shortcut => {
      const keys = Array.isArray(shortcut.key) ? shortcut.key : [shortcut.key];
      const keyMatches = keys.some(k => k.toLowerCase() === event.key.toLowerCase());

      if (!keyMatches) return;

      // ─── CHECK MODIFIERS ───
      const ctrlMatch = shortcut.ctrl === undefined || shortcut.ctrl === event.ctrlKey;
      const metaMatch = shortcut.meta === undefined || shortcut.meta === event.metaKey;
      const shiftMatch = shortcut.shift === undefined || shortcut.shift === event.shiftKey;
      const altMatch = shortcut.alt === undefined || shortcut.alt === event.altKey;

      if (ctrlMatch && metaMatch && shiftMatch && altMatch) {
        if (shortcut.preventDefault !== false) {
          event.preventDefault();
        }
        shortcut.handler(event);
      }
    });
  }, [shortcuts, enabled, ignoreInputElements]);

  useEffect(() => {
    if (!enabled) return;

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress, enabled]);
};

// ─── COMMON SHORTCUT PRESETS ───
export const COMMON_SHORTCUTS = {
  ZOOM_IN: ['+', '='],
  ZOOM_OUT: ['-', '_'],
  FULLSCREEN: ['f', 'F'],
  RESET: ['r', 'R'],
  NAVIGATE_LEFT: 'ArrowLeft',
  NAVIGATE_RIGHT: 'ArrowRight',
  NAVIGATE_UP: 'ArrowUp',
  NAVIGATE_DOWN: 'ArrowDown',
  HOME: 'Home',
  END: 'End',
  ESCAPE: 'Escape'
};