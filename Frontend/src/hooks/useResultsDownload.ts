import { useCallback } from 'react';
import { apiClient } from '@/services/api';
import { useDownloadExport } from './useDownloadExport';
import type { AnalysisResults } from '@/types/analysis';

export const useResultsDownload = (jobId: string, results: AnalysisResults | null) => {
  const { downloadData, convertToCSV } = useDownloadExport();

  const downloadResults = useCallback(async (format: string) => {
    try {
      if (format === 'report') {
        const response = await apiClient.get<{ download_url: string }>(
          `/analysis/download/${jobId}?format=report`
        );
        if (response.data?.download_url) {
          window.open(response.data.download_url, '_blank');
        }
        return;
      }

      const response = await apiClient.get<{ download_url: string }>(
        `/analysis/download/${jobId}?format=${format}`
      );

      if (response.data?.download_url) {
        window.open(response.data.download_url, '_blank');
      } else {
        handleLocalDownload(format);
      }
    } catch {
      handleLocalDownload(format);
    }
  }, [jobId]);

  const handleLocalDownload = useCallback((format: string) => {
    if (!results) return;

    const data = format === 'json'
      ? results
      : convertToCSV(results.hfo_events);

    downloadData(data, {
      filename: `analysis_results_${jobId}`,
      format: format as 'csv' | 'json'
    });
  }, [results, jobId, convertToCSV, downloadData]);

  return { downloadResults };
};