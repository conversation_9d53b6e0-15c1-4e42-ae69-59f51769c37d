import { useState, useCallback } from 'react';
import type { AnalysisParameters } from '@/components/analysis/EnhancedAnalysisParametersForm';

interface ValidationErrors {
  [key: string]: string | undefined;
}

export const useFormValidation = () => {
  const [errors, setErrors] = useState<ValidationErrors>({});

  const validateParameters = useCallback((params: AnalysisParameters): boolean => {
    const newErrors: ValidationErrors = {};

    // Frequency validation
    if (params.frequency.low_cutoff >= params.frequency.high_cutoff) {
      newErrors.frequency = 'Low cutoff must be less than high cutoff';
    }

    // Threshold validation
    if (params.thresholds.duration < 1) {
      newErrors.duration = 'Duration must be at least 1ms';
    }

    // Channel selection validation
    if (params.channelSelection && params.channelSelection.selectedChannels.length === 0) {
      newErrors.channels = 'At least one channel must be selected';
    }

    // Time segment validation
    if (params.timeSegment?.mode === 'start_end_times') {
      if ((params.timeSegment.endTime || 0) <= (params.timeSegment.startTime || 0)) {
        newErrors.timeSegment = 'End time must be greater than start time';
      }
    }

    // Montage validation
    if (params.montage.type === 'referential' && !params.montage.reference) {
      newErrors.reference = 'Reference channel is required for referential montage';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, []);

  const clearError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  return {
    errors,
    validateParameters,
    clearError,
  };
};