import { useState, useCallback } from 'react';
import { fileService } from '@/services/api';

interface FileMetadata {
  channels: string[];
  samplingRate: number;
  duration: number;
}

interface UseFileMetadataReturn {
  metadata: FileMetadata | null;
  loading: boolean;
  error: string | null;
  fetchMetadata: (fileKey: string) => Promise<void>;
  clearMetadata: () => void;
}

const DEFAULT_METADATA: FileMetadata = {
  channels: [],
  samplingRate: 256,
  duration: 600
};

export const useFileMetadata = (): UseFileMetadataReturn => {
  const [metadata, setMetadata] = useState<FileMetadata | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ─── FETCH FILE METADATA ───
  const fetchMetadata = useCallback(async (fileKey: string) => {
    setLoading(true);
    setError(null);
    setMetadata(null);

    try {
      const response = await fileService.getFileMetadata(fileKey);

      if (response) {
        setMetadata({
          channels: response.channels || DEFAULT_METADATA.channels,
          samplingRate: response.sampling_rate || DEFAULT_METADATA.samplingRate,
          duration: response.duration_seconds || DEFAULT_METADATA.duration,
        });
      } else {
        setMetadata(DEFAULT_METADATA);
      }
    } catch {
      setError('Failed to fetch file metadata');
      setMetadata(DEFAULT_METADATA);
    } finally {
      setLoading(false);
    }
  }, []);

  // ─── CLEAR METADATA ───
  const clearMetadata = useCallback(() => {
    setMetadata(null);
    setError(null);
  }, []);

  return {
    metadata,
    loading,
    error,
    fetchMetadata,
    clearMetadata
  };
};