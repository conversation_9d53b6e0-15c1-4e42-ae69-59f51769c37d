import { useState, useCallback } from 'react';
import type { HFOEvent } from '@/types/hfo';

interface DownloadOptions {
  filename?: string;
  format?: 'csv' | 'json' | 'png';
  quality?: number;
}

interface UseDownloadExportReturn {
  isDownloading: boolean;
  downloadData: (data: unknown, options?: DownloadOptions) => void;
  downloadCSV: (data: Record<string, unknown>[], headers: string[], filename?: string) => void;
  downloadJSON: (data: unknown, filename?: string) => void;
  downloadImage: (element: HTMLElement, options?: DownloadOptions) => Promise<void>;
  convertToCSV: (events: HFOEvent[]) => string;
}

export const useDownloadExport = (): UseDownloadExportReturn => {
  const [isDownloading, setIsDownloading] = useState(false);

  // ─── CONVERT HFO EVENTS TO CSV ───
  const convertToCSV = useCallback((events: HFOEvent[]): string => {
    const headers = ['Channel', 'Start Time', 'End Time', 'Peak Frequency', 'Amplitude', 'Type'];
    const rows = events.map(e => [
      e.channel,
      e.start_time.toFixed(3),
      e.end_time.toFixed(3),
      e.peak_frequency.toFixed(1),
      e.amplitude.toFixed(2),
      e.type || 'accepted'
    ]);

    return [headers, ...rows]
      .map(row => row.join(','))
      .join('\n');
  }, []);

  // ─── GENERIC DOWNLOAD FUNCTION ───
  const downloadData = useCallback((
    data: unknown,
    options: DownloadOptions = {}
  ) => {
    const {
      filename = 'download',
      format = 'json'
    } = options;

    setIsDownloading(true);

    try {
      let content: string;
      let mimeType: string;

      switch (format) {
        case 'csv':
          content = typeof data === 'string' ? data : convertToCSV(data as HFOEvent[]);
          mimeType = 'text/csv';
          break;
        case 'json':
          content = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
          mimeType = 'application/json';
          break;
        default:
          content = String(data);
          mimeType = 'text/plain';
      }

      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');

      link.href = url;
      link.download = `${filename}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } finally {
      setIsDownloading(false);
    }
  }, [convertToCSV]);

  // ─── CSV DOWNLOAD ───
  const downloadCSV = useCallback((
    data: Record<string, unknown>[],
    headers: string[],
    filename = 'data'
  ) => {
    const csvContent = [
      headers,
      ...data.map(row =>
        headers.map(header =>
          typeof row[header] === 'string' && row[header].includes(',')
            ? `"${row[header]}"`
            : row[header] ?? ''
        )
      )
    ].map(row => row.join(','))
     .join('\n');

    downloadData(csvContent, { filename, format: 'csv' });
  }, [downloadData]);

  // ─── JSON DOWNLOAD ───
  const downloadJSON = useCallback((
    data: unknown,
    filename = 'data'
  ) => {
    downloadData(data, { filename, format: 'json' });
  }, [downloadData]);

  // ─── IMAGE DOWNLOAD ───
  const downloadImage = useCallback(async (
    element: HTMLElement,
    options: DownloadOptions = {}
  ) => {
    const {
      filename = 'image'
    } = options;

    setIsDownloading(true);

    try {
      // ─── CHECK IF PLOTLY ELEMENT ───
      if ('_fullLayout' in element && '_fullData' in element) {
        const Plotly = await import('plotly.js');
        await Plotly.downloadImage(element as HTMLElement & { _fullLayout: unknown; _fullData: unknown }, {
          format: 'png',
          width: 1920,
          height: 1200,
          filename
        });
      } else {
        // ─── FALLBACK TO HTML2CANVAS IF NEEDED ───
        // Note: html2canvas dependency needs to be installed for screenshot support
      }
    } catch {
      // Failed to download image
    } finally {
      setIsDownloading(false);
    }
  }, []);

  return {
    isDownloading,
    downloadData,
    downloadCSV,
    downloadJSON,
    downloadImage,
    convertToCSV
  };
};