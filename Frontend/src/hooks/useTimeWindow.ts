import { useState, useCallback, useMemo } from 'react';

interface UseTimeWindowOptions {
  initialWindow?: [number, number];
  duration: number;
  minWindowSize?: number;
  maxWindowSize?: number;
  chunkSize?: number;
}

interface TimeWindowControls {
  timeWindow: [number, number];
  timeWindowSize: number;
  currentChunk: number;
  setTimeWindow: (window: [number, number]) => void;
  setWindow: (start: number, width: number, options?: { updateSize?: boolean }) => void;
  zoomWindow: (direction: 'in' | 'out', anchor?: number) => void;
  panWindow: (direction: -1 | 1) => void;
  jumpToBoundary: (position: 'start' | 'end') => void;
  resetWindow: () => void;
  navigateToTime: (time: number) => void;
  canZoomIn: boolean;
  canZoomOut: boolean;
  canPanLeft: boolean;
  canPanRight: boolean;
}

// ─── CONSTANTS ───
const DEFAULT_MIN_WINDOW_SECONDS = 0.5;
const ZOOM_IN_FACTOR = 0.8;
const ZOOM_OUT_FACTOR = 1.25;
const PAN_STEP_RATIO = 0.2;
const DEFAULT_CHUNK_SECONDS = 10;

export const useTimeWindow = (options: UseTimeWindowOptions): TimeWindowControls => {
  const {
    initialWindow,
    duration,
    minWindowSize = DEFAULT_MIN_WINDOW_SECONDS,
    maxWindowSize = duration,
    chunkSize = DEFAULT_CHUNK_SECONDS
  } = options;

  const [timeWindow, setTimeWindow] = useState<[number, number]>(
    initialWindow || [0, Math.min(5, duration || 5)]
  );
  const [timeWindowSize, setTimeWindowSize] = useState(
    initialWindow ? initialWindow[1] - initialWindow[0] : Math.min(5, duration || 5)
  );
  const [currentChunk, setCurrentChunk] = useState(0);

  // ─── SET WINDOW WITH BOUNDS CHECKING ───
  const setWindow = useCallback(
    (start: number, width: number, { updateSize = true }: { updateSize?: boolean } = {}) => {
      if (!duration) return;

      const clampedWidth = Math.max(minWindowSize, Math.min(width, maxWindowSize));
      const maxStart = Math.max(0, duration - clampedWidth);
      const clampedStart = Math.min(Math.max(0, start), maxStart);
      const clampedEnd = clampedStart + clampedWidth;

      setTimeWindow([clampedStart, clampedEnd]);
      if (updateSize) {
        setTimeWindowSize(clampedWidth);
      }
      setCurrentChunk(Math.floor(clampedStart / chunkSize));
    },
    [duration, minWindowSize, maxWindowSize, chunkSize]
  );

  // ─── ZOOM OPERATIONS ───
  const zoomWindow = useCallback(
    (direction: 'in' | 'out', anchor?: number) => {
      if (!duration) return;

      const currentSize = Math.max(timeWindow[1] - timeWindow[0], minWindowSize);
      const zoomFactor = direction === 'in' ? ZOOM_IN_FACTOR : ZOOM_OUT_FACTOR;
      const proposedSize = currentSize * zoomFactor;
      const targetSize = direction === 'in'
        ? Math.max(minWindowSize, proposedSize)
        : Math.min(maxWindowSize, proposedSize);

      const focus = anchor ?? (timeWindow[0] + timeWindow[1]) / 2;
      const start = focus - targetSize / 2;
      setWindow(start, targetSize);
    },
    [duration, timeWindow, minWindowSize, maxWindowSize, setWindow]
  );

  // ─── PAN OPERATIONS ───
  const panWindow = useCallback(
    (direction: -1 | 1) => {
      if (!duration || timeWindowSize === 0) return;

      const step = Math.max(timeWindowSize * PAN_STEP_RATIO, duration * 0.01);
      setWindow(timeWindow[0] + direction * step, timeWindowSize, { updateSize: false });
    },
    [duration, timeWindow, timeWindowSize, setWindow]
  );

  // ─── JUMP OPERATIONS ───
  const jumpToBoundary = useCallback(
    (position: 'start' | 'end') => {
      if (!duration || timeWindowSize === 0) return;

      if (position === 'start') {
        setWindow(0, timeWindowSize, { updateSize: false });
      } else {
        setWindow(duration - timeWindowSize, timeWindowSize, { updateSize: false });
      }
    },
    [duration, timeWindowSize, setWindow]
  );

  // ─── NAVIGATION ───
  const navigateToTime = useCallback(
    (time: number) => {
      if (!duration || timeWindowSize === 0) return;

      const halfWindow = timeWindowSize / 2;
      setWindow(time - halfWindow, timeWindowSize, { updateSize: false });
    },
    [duration, timeWindowSize, setWindow]
  );

  // ─── RESET ───
  const resetWindow = useCallback(() => {
    const defaultWindow = Math.min(5, duration || 5);
    setWindow(0, defaultWindow);
  }, [duration, setWindow]);

  // ─── COMPUTED FLAGS ───
  const canZoomIn = useMemo(
    () => timeWindowSize > minWindowSize,
    [timeWindowSize, minWindowSize]
  );

  const canZoomOut = useMemo(
    () => timeWindowSize < maxWindowSize,
    [timeWindowSize, maxWindowSize]
  );

  const canPanLeft = useMemo(
    () => timeWindow[0] > 0,
    [timeWindow]
  );

  const canPanRight = useMemo(
    () => timeWindow[1] < duration,
    [timeWindow, duration]
  );

  return {
    timeWindow,
    timeWindowSize,
    currentChunk,
    setTimeWindow,
    setWindow,
    zoomWindow,
    panWindow,
    jumpToBoundary,
    resetWindow,
    navigateToTime,
    canZoomIn,
    canZoomOut,
    canPanLeft,
    canPanRight
  };
};