import { useState, useEffect, useCallback } from 'react';
import type { RefObject } from 'react';

interface UseFullscreenOptions {
  onEnter?: () => void;
  onExit?: () => void;
  onError?: (error: Error) => void;
}

interface FullscreenControls {
  isFullscreen: boolean;
  enterFullscreen: () => Promise<void>;
  exitFullscreen: () => Promise<void>;
  toggleFullscreen: () => Promise<void>;
  canUseFullscreen: boolean;
}

interface VendorDocument extends Document {
  webkitFullscreenEnabled?: boolean;
  mozFullScreenEnabled?: boolean;
  msFullscreenEnabled?: boolean;
  webkitExitFullscreen?: () => Promise<void>;
  mozCancelFullScreen?: () => Promise<void>;
  msExitFullscreen?: () => Promise<void>;
  webkitFullscreenElement?: Element | null;
  mozFullScreenElement?: Element | null;
  msFullscreenElement?: Element | null;
}

interface VendorHTMLElement extends HTMLElement {
  webkitRequestFullscreen?: () => Promise<void>;
  mozRequestFullScreen?: () => Promise<void>;
  msRequestFullscreen?: () => Promise<void>;
}

export const useFullscreen = <T extends HTMLElement = HTMLDivElement>(
  elementRef: RefObject<T | null>,
  options: UseFullscreenOptions = {}
): FullscreenControls => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const { onEnter, onExit, onError } = options;
  const doc = document as VendorDocument;

  // ─── CHECK FULLSCREEN SUPPORT ───
  const canUseFullscreen = typeof document !== 'undefined' &&
    !!(doc.fullscreenEnabled ||
       doc.webkitFullscreenEnabled ||
       doc.mozFullScreenEnabled ||
       doc.msFullscreenEnabled);

  // ─── ENTER FULLSCREEN ───
  const enterFullscreen = useCallback(async () => {
    if (!elementRef.current || !canUseFullscreen) {
      return;
    }

    try {
      const element = elementRef.current as VendorHTMLElement;

      if (element.requestFullscreen) {
        await element.requestFullscreen();
      } else if (element.webkitRequestFullscreen) {
        await element.webkitRequestFullscreen();
      } else if (element.mozRequestFullScreen) {
        await element.mozRequestFullScreen();
      } else if (element.msRequestFullscreen) {
        await element.msRequestFullscreen();
      }

      onEnter?.();
    } catch (error) {
      onError?.(error as Error);
    }
  }, [elementRef, canUseFullscreen, onEnter, onError]);

  // ─── EXIT FULLSCREEN ───
  const exitFullscreen = useCallback(async () => {
    if (!doc.fullscreenElement &&
        !doc.webkitFullscreenElement &&
        !doc.mozFullScreenElement &&
        !doc.msFullscreenElement) {
      return;
    }

    try {
      if (doc.exitFullscreen) {
        await doc.exitFullscreen();
      } else if (doc.webkitExitFullscreen) {
        await doc.webkitExitFullscreen();
      } else if (doc.mozCancelFullScreen) {
        await doc.mozCancelFullScreen();
      } else if (doc.msExitFullscreen) {
        await doc.msExitFullscreen();
      }

      onExit?.();
    } catch (error) {
      onError?.(error as Error);
    }
  }, [onExit, onError]);

  // ─── TOGGLE FULLSCREEN ───
  const toggleFullscreen = useCallback(async () => {
    if (isFullscreen) {
      await exitFullscreen();
    } else {
      await enterFullscreen();
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen]);

  // ─── FULLSCREEN CHANGE LISTENER ───
  useEffect(() => {
    const handleFullscreenChange = () => {
      const fullscreenElement = doc.fullscreenElement ||
                               doc.webkitFullscreenElement ||
                               doc.mozFullScreenElement ||
                               doc.msFullscreenElement;

      const isCurrentlyFullscreen = !!fullscreenElement;
      setIsFullscreen(isCurrentlyFullscreen);

      if (isCurrentlyFullscreen) {
        onEnter?.();
      } else {
        onExit?.();
      }
    };

    const handleFullscreenError = () => {
      onError?.(new Error('Fullscreen operation failed'));
    };

    // ─── ADD EVENT LISTENERS ───
    const events = [
      'fullscreenchange',
      'webkitfullscreenchange',
      'mozfullscreenchange',
      'MSFullscreenChange'
    ];

    const errorEvents = [
      'fullscreenerror',
      'webkitfullscreenerror',
      'mozfullscreenerror',
      'MSFullscreenError'
    ];

    events.forEach(event => {
      document.addEventListener(event, handleFullscreenChange);
    });

    errorEvents.forEach(event => {
      document.addEventListener(event, handleFullscreenError);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleFullscreenChange);
      });
      errorEvents.forEach(event => {
        document.removeEventListener(event, handleFullscreenError);
      });
    };
  }, [onEnter, onExit, onError]);

  return {
    isFullscreen,
    enterFullscreen,
    exitFullscreen,
    toggleFullscreen,
    canUseFullscreen
  };
};