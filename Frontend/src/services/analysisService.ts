import axios from "axios";
import { API_CONFIG } from "../constants";
import type { AnalysisJob, AnalysisResults, ResultsMetadata } from "../types/analysis";

export interface BatchAnalysisRequest {
  files: Array<{
    file_key: string;
    parameters?: Record<string, unknown>;
  }>;
}

export interface BatchAnalysisResponse {
  batch_id: string;
  job_ids: string[];
  status: string;
  message: string;
}

class AnalysisService {
  private baseURL: string;

  constructor() {
    this.baseURL = API_CONFIG.BASE_URL;
  }

  /**
   * Submit a single file for analysis
   */
  async submitAnalysis(fileKey: string, parameters?: Record<string, unknown>): Promise<{
    job_id: string;
    status: string;
    message: string;
  }> {
    const response = await axios.post(`${this.baseURL}/analysis/submit`, {
      file_key: fileKey,
      parameters: parameters || {}
    });
    return response.data;
  }

  /**
   * Submit multiple files for batch analysis
   */
  async submitBatchAnalysis(request: BatchAnalysisRequest): Promise<BatchAnalysisResponse> {
    const response = await axios.post(`${this.baseURL}/analysis/batch`, request);
    return response.data;
  }

  /**
   * Get all analysis jobs
   */
  async getJobs(userEmail?: string): Promise<AnalysisJob[]> {
    const params = userEmail ? { user_email: userEmail } : {};
    const response = await axios.get(`${this.baseURL}/analysis/jobs`, { params });
    return response.data;
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<AnalysisJob> {
    const response = await axios.get(`${this.baseURL}/analysis/status/${jobId}`);
    return response.data;
  }

  /**
   * Get batch status
   */
  async getBatchStatus(batchId: string): Promise<{
    batch_id: string;
    total_jobs: number;
    completed: number;
    failed: number;
    processing: number;
    pending: number;
    jobs: Array<{
      job_id: string;
      file_key: string;
      status: string;
      hfo_count?: number;
      error?: string;
    }>;
  }> {
    const response = await axios.get(`${this.baseURL}/analysis/batch-status/${batchId}`);
    return response.data;
  }

  /**
   * Get analysis results metadata with presigned URLs (lightweight)
   * This is the first step in the two-step process
   */
  async getResultsMetadata(jobId: string): Promise<ResultsMetadata> {
    const response = await axios.get(`${this.baseURL}/analysis/results/${jobId}`);
    return response.data;
  }

  /**
   * Fetch full results directly from S3 using presigned URL
   * This is the second step in the two-step process
   */
  async fetchFullResultsFromS3(presignedUrl: string): Promise<AnalysisResults> {
    try {
      // Fetch directly from S3 using the presigned URL
      const response = await axios.get(presignedUrl, {
        timeout: 60000, // 60 second timeout for large files
        maxContentLength: Infinity,
        maxBodyLength: Infinity,
      });
      return response.data;
    } catch {
      throw new Error('Failed to fetch analysis results from S3');
    }
  }

  /**
   * Get analysis results - handles the two-step process automatically
   * First fetches metadata, then fetches full results from S3
   */
  async getAnalysisResults(jobId: string): Promise<AnalysisResults> {
    // Step 1: Get metadata and presigned URLs from API
    const metadata = await this.getResultsMetadata(jobId);

    // Step 2: Fetch full results directly from S3
    const fullResults = await this.fetchFullResultsFromS3(metadata.data_urls.full_results);

    // Optionally merge metadata into results if needed
    return {
      ...fullResults,
      _metadata: metadata // Include metadata info
    };
  }

  /**
   * Get download URL for results in specific format
   */
  async getDownloadUrl(jobId: string, format: 'json' | 'csv' | 'report' = 'json'): Promise<{
    download_url: string;
    format: string;
  }> {
    const response = await axios.get(`${this.baseURL}/analysis/download/${jobId}`, {
      params: { format }
    });
    return response.data;
  }

  /**
   * Poll job status until completion
   */
  async pollJobStatus(
    jobId: string,
    onProgress?: (job: AnalysisJob) => void,
    intervalMs: number = 2000,
    maxAttempts: number = 150 // 5 minutes with 2 second intervals
  ): Promise<AnalysisJob> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const job = await this.getJobStatus(jobId);

          if (onProgress) {
            onProgress(job);
          }

          if (job.status === 'completed') {
            resolve(job);
            return;
          }

          if (job.status === 'failed') {
            reject(new Error(job.error_message || 'Analysis failed'));
            return;
          }

          attempts++;
          if (attempts >= maxAttempts) {
            reject(new Error('Polling timeout - job is still processing'));
            return;
          }

          setTimeout(poll, intervalMs);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

export const analysisService = new AnalysisService();
export default analysisService;