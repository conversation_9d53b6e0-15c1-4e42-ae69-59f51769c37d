import { fileService } from "./api";
import type { CompletedPart } from "@/types/api";
import { calculateOptimalSettings } from "@/utils/upload/chunkCalculator";
import { uploadPartWithRetry, executeWithWorkerPool } from "@/utils/upload/partUploader";
import { prefetchPartUrls } from "@/utils/upload/urlPrefetcher";
import { UPLOAD_CONFIG } from "@/constants";

export class MultipartUploadService {
  // ─── UPLOAD FILE ───
  static async uploadFile(
    file: File,
    onProgress?: (percent: number) => void
  ): Promise<{ key: string; upload_id: string }> {
    let uploadId: string | undefined;
    let key: string | undefined;

    try {
      // ─── INITIATE ───
      const initResponse = await fileService.initiateMultipart(file.name, file.size);
      uploadId = initResponse.upload_id;
      key = initResponse.key;

      // ─── CALCULATE SETTINGS ───
      const { chunkSize, totalParts, concurrency } = calculateOptimalSettings(file.size);

      // ─── UPLOAD PARTS ───
      const parts = await this.uploadParts(
        file,
        key,
        uploadId,
        chunkSize,
        totalParts,
        concurrency,
        onProgress
      );

      // ─── COMPLETE ───
      await fileService.completeMultipart(key, uploadId, parts);
      onProgress?.(100);

      return { key, upload_id: uploadId };
    } catch (error) {
      if (key && uploadId) {
        await this.abortUpload(key, uploadId);
      }
      throw error;
    }
  }

  // ─── UPLOAD PARTS ───
  private static async uploadParts(
    file: File,
    key: string,
    uploadId: string,
    chunkSize: number,
    totalParts: number,
    concurrency: number,
    onProgress?: (percent: number) => void
  ): Promise<CompletedPart[]> {
    const parts: CompletedPart[] = [];
    const partProgress = new Map<number, number>();

    // ─── PROGRESS TRACKING ───
    const updateProgress = () => {
      if (!onProgress) return;
      const totalProgress = Array.from(partProgress.values()).reduce((sum, val) => sum + val, 0);
      const percent = Math.min(99, Math.round((totalProgress / file.size) * 100));
      onProgress(percent);
    };

    // ─── PREFETCH URLS ───
    const partUrls = await prefetchPartUrls(key, uploadId, totalParts);

    // ─── UPLOAD PART FUNCTION ───
    const uploadPart = async (partNumber: number) => {
      const start = (partNumber - 1) * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const blob = file.slice(start, end);
      const url = partUrls.get(partNumber)!;

      const eTag = await uploadPartWithRetry(
        blob,
        url,
        partNumber,
        (loaded) => {
          partProgress.set(partNumber, loaded);
          updateProgress();
        }
      );

      parts.push({ ETag: eTag, PartNumber: partNumber });
    };

    // ─── EXECUTE WITH WORKER POOL ───
    await executeWithWorkerPool(totalParts, concurrency, uploadPart);

    // ─── SORT PARTS ───
    return parts.sort((a, b) => a.PartNumber - b.PartNumber);
  }

  // ─── ABORT UPLOAD ───
  static async abortUpload(key: string, uploadId: string): Promise<void> {
    try {
      await Promise.race([
        fileService.abortMultipart(key, uploadId),
        new Promise(resolve =>
          setTimeout(resolve, UPLOAD_CONFIG.MULTIPART.TIMEOUTS.ABORT)
        )
      ]);
    } catch {
      // Best effort - silently handle errors
    }
  }
}