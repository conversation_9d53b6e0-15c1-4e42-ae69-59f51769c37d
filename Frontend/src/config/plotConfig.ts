import type { Config, Layout } from 'plotly.js';

interface PlotLayoutOptions {
  title?: string;
  timeWindow: [number, number];
  selectedChannels: string[];
  channelOffset: number;
  metadata?: {
    filename?: string;
    montage?: string;
    frequency_band?: string;
  };
}

// ─── CREATE PLOT LAYOUT ───
export const createPlotLayout = (options: PlotLayoutOptions): Partial<Layout> => {
  const { title, timeWindow, selectedChannels, channelOffset, metadata } = options;

  return {
    title: {
      text: title || metadata?.filename || 'HFO Analysis',
      font: {
        family: 'system-ui, -apple-system, sans-serif',
        size: 16,
        color: '#1e293b',
      },
    },
    xaxis: {
      title: {
        text: 'Time (seconds)',
        font: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 12,
          color: '#64748b',
        },
      },
      range: timeWindow,
      gridcolor: '#e2e8f0',
      gridwidth: 0.5,
      linecolor: '#cbd5e1',
      linewidth: 1,
      tickfont: {
        family: 'system-ui, -apple-system, sans-serif',
        size: 10,
        color: '#64748b',
      },
      zeroline: false,
    },
    yaxis: {
      title: {
        text: 'Channels',
        font: {
          family: 'system-ui, -apple-system, sans-serif',
          size: 12,
          color: '#64748b',
        },
      },
      tickmode: 'array' as const,
      tickvals: selectedChannels.map((_, i) => i * channelOffset),
      ticktext: selectedChannels,
      range: [selectedChannels.length * channelOffset + channelOffset / 2, -channelOffset / 2],
      autorange: false,
      gridcolor: '#f8fafc',
      gridwidth: 0.5,
      linecolor: '#cbd5e1',
      linewidth: 1,
      tickfont: {
        family: 'system-ui, -apple-system, sans-serif',
        size: 10,
        color: '#64748b',
      },
      zeroline: false,
    },
    height: Math.max(600, selectedChannels.length * 80 + 150),
    margin: { l: 100, r: 30, t: 50, b: 60 },
    hovermode: 'closest' as const,
    hoverlabel: {
      bgcolor: 'white',
      font: {
        family: 'system-ui, -apple-system, sans-serif',
        size: 11,
        color: '#1e293b',
      },
      bordercolor: '#e2e8f0',
    },
    showlegend: false,
    plot_bgcolor: '#ffffff',
    paper_bgcolor: '#ffffff',
    annotations: metadata
      ? [
          {
            text: `Montage: ${metadata.montage || 'N/A'} | Frequency: ${metadata.frequency_band || 'N/A'}`,
            showarrow: false,
            x: 0,
            y: 1.05,
            xref: 'paper' as const,
            yref: 'paper' as const,
            xanchor: 'left' as const,
            font: {
              family: 'system-ui, -apple-system, sans-serif',
              size: 10,
              color: '#64748b',
            },
          },
        ]
      : [],
  };
};

// ─── CREATE PLOT CONFIG ───
export const createPlotConfig = (filename?: string): Partial<Config> => {
  const exportFilename = filename
    ? `hfo_analysis_${filename.replace('.edf', '')}_${new Date().toISOString().split('T')[0]}`
    : `hfo_analysis_export_${new Date().toISOString().split('T')[0]}`;

  return {
    displayModeBar: true,
    displaylogo: false,
    modeBarButtonsToRemove: ['lasso2d' as const, 'select2d' as const, 'autoScale2d' as const],
    modeBarButtonsToAdd: [],
    toImageButtonOptions: {
      format: 'png' as const,
      filename: exportFilename,
      height: 1200,
      width: 1920,
      scale: 2,
    },
    responsive: true,
    scrollZoom: true,
    doubleClick: 'reset' as const,
    showTips: false,
    plotGlPixelRatio: 1,
  };
};

// ─── CHANNEL OFFSET CALCULATION ───
export const calculateChannelOffset = (
  selectedChannelsCount: number,
  gain: number
): number => {
  const baseOffset = Math.max(100, 800 / Math.max(selectedChannelsCount, 1));
  return baseOffset / (gain / 20);
};