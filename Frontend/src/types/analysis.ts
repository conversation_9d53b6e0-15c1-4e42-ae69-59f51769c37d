export interface AnalysisJob {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  file_key?: string;
  created_at: string;
  updated_at?: string;
  completed_at?: string;
  hfo_count?: number;
  results_url?: string;
  error_message?: string;
}

// Import HFOEvent from hfo.ts
import type { HFOEvent } from './hfo';

// Re-export for consistency
export type HfoEvent = HFOEvent;

export interface AnalysisMetadata {
  filename: string;
  sampling_rate: number;
  duration_seconds: number;
  channels: string[];
  processing_time?: number;
  analysis_date?: string;
  parameters?: Record<string, unknown>;
}

export interface AnalysisStatistics {
  total_hfos: number;
  hfo_density: number;
  channels_with_hfos: string[];
  hfo_rate_per_channel: Record<string, number>;
  hfo_types?: Record<string, number>;
  average_duration?: number;
  average_frequency?: number;
}

export interface ChannelData {
  [channelName: string]: number[];
}

export interface SamplingInfo {
  downsampled: boolean;
  effective_sampling_rate: number;
  original_sampling_rate: number;
  duration_seconds: number;
  downsample_factor?: number;
}

export interface ResultsMetadata {
  job_id: string;
  file_size_bytes: number;
  last_modified: string;
  metadata?: {
    filename?: string;
    sampling_rate?: number;
    duration_seconds?: number;
    channels?: string[];
    processing_time?: number;
  };
  statistics?: {
    total_hfos?: number;
    hfo_density?: number;
    channels_with_hfos?: string[];
    hfo_rate_per_channel?: Record<string, number>;
  };
  data_urls: {
    full_results: string;
    hfo_events_csv: string;
    analysis_report_csv: string;
  };
  message: string;
}

export interface ResultsMetadataWrapper {
  job_id: string;
  status: string;
  summary?: {
    total_hfos?: number;
    channels_analyzed?: number;
    duration_analyzed?: number;
  };
  data_urls?: {
    full_results?: string;
    results_json?: string;
    hfo_events_csv?: string;
    analysis_report_csv?: string;
  };
  parameters?: AnalysisParameters;
  created_at?: string;
  completed_at?: string;
}

export interface AnalysisResults {
  metadata: AnalysisMetadata;
  statistics: AnalysisStatistics;
  channel_data?: ChannelData;
  sampling_info?: SamplingInfo;
  hfo_events: HfoEvent[];
  time_axis?: number[];
  download_urls?: {
    results_json?: string;
    hfo_events_csv?: string;
    analysis_report_csv?: string;
  };
  _metadata?: Partial<ResultsMetadataWrapper> | ResultsMetadata; // Additional metadata from the two-step process
}

export interface AnalysisParameters {
  montage?: string;
  frequency_min?: number;
  frequency_max?: number;
  threshold?: number;
  window_size?: number;
  overlap?: number;
  detection_method?: string;
  [key: string]: unknown;
}