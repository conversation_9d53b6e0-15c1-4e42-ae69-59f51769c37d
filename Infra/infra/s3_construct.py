# S3 storage construct for EDF files
from aws_cdk import Duration, RemovalP<PERSON>y
from aws_cdk import aws_s3 as s3
from constructs import Construct

from .base_construct import BaseConstruct
from .config import (
    ALLOWED_FILE_PATTERNS,
    DEFAULT_ALLOWED_ORIGINS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
)


class S3StorageConstruct(BaseConstruct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        *,
        bucket_name: str | None = None,
        allowed_origins: list[str] | None = None,
    ) -> None:
        super().__init__(scope, construct_id)

        self.allowed_origins = allowed_origins or DEFAULT_ALLOWED_ORIGINS

        self._create_s3_bucket(bucket_name)
        self._create_backend_role()
        self._grant_permissions()
        self._create_outputs()

    def _create_s3_bucket(self, bucket_name: str | None) -> None:
        """Create S3 bucket with security and lifecycle configurations."""
        self.edf_bucket = s3.Bucket(
            self,
            "EDFStorageBucket",
            bucket_name=bucket_name,
            versioned=True,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            lifecycle_rules=[
                s3.LifecycleRule(
                    id="DeleteIncompleteMultipartUploads",
                    abort_incomplete_multipart_upload_after=Duration.days(
                        S3_MULTIPART_UPLOAD_CLEANUP_DAYS
                    ),
                )
            ],
            cors=self.create_s3_cors_rules(self.allowed_origins),
            transfer_acceleration=True,
            removal_policy=RemovalPolicy.RETAIN,
        )

    def _create_backend_role(self) -> None:
        """Create IAM role for backend service."""
        self.backend_role = self.create_service_role(
            "BackendServiceRole",
            ["ec2.amazonaws.com", "lambda.amazonaws.com"],
            "Role for backend service to access S3 for EDF file operations",
        )

    def _grant_permissions(self) -> None:
        """Grant S3 permissions to the backend role."""
        self.edf_bucket.grant_read_write(self.backend_role)

        # Presigned URL permissions
        self.backend_role.add_to_policy(
            self.create_s3_presigned_policy(
                self.edf_bucket.bucket_arn, ALLOWED_FILE_PATTERNS
            )
        )

    def _create_outputs(self) -> None:
        """Create CloudFormation outputs."""
        self.create_output(
            "BucketName",
            self.edf_bucket.bucket_name,
            "Name of the S3 bucket for EDF file storage",
        )

        self.create_output(
            "BucketArn",
            self.edf_bucket.bucket_arn,
            "ARN of the S3 bucket",
        )

        self.create_output(
            "BackendRoleArn",
            self.backend_role.role_arn,
            "ARN of the backend service role",
        )
