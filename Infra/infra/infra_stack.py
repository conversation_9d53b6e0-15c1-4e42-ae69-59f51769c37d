from aws_cdk import <PERSON>fn<PERSON><PERSON><PERSON>, Stack, Tags
from aws_cdk import aws_ec2 as ec2
from constructs import Construct

from .config import (
    DEFAULT_ALLOWED_ORIGINS,
    ENVIRONMENT,
    PROJECT_NAME,
    PURPOSE,
    SENDER_EMAIL,
    VPC_MAX_AZS,
    VPC_NAT_GATEWAYS,
)
from .dynamodb_construct import DynamoDBConstruct
from .ecs_fargate_construct import ECSFargateConstruct
from .lambda_construct import LambdaApiConstruct
from .s3_construct import S3StorageConstruct
from .ses_construct import SESConstruct
from .sqs_construct import SQSConstruct
from .static_site_construct import StaticSiteConstruct


class InfraStack(Stack):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id, **kwargs)

        # Setup frontend and get allowed origins
        allowed_origins = self._create_frontend_infrastructure()
        s3_storage = self._create_storage_infrastructure(allowed_origins)
        lambda_api = self._create_api_infrastructure(s3_storage, allowed_origins)

        # Data storage services
        dynamodb = DynamoDBConstruct(self, "DynamoDB")
        sqs = SQSConstruct(self, "SQS")
        ses = SESConstruct(self, "SES", sender_email=SENDER_EMAIL)

        # Processing infrastructure
        vpc = self._create_vpc()
        ecs_fargate = self._create_processing_infrastructure(
            vpc, sqs, s3_storage, dynamodb, ses
        )

        # Configure services
        self._configure_permissions(dynamodb, sqs, ses, lambda_api)
        self._configure_lambda_environment(lambda_api, dynamodb, sqs, ses)
        self._create_stack_outputs(ecs_fargate)
        self._apply_tags()

    def _create_frontend_infrastructure(self) -> list[str]:
        """Create static site and return allowed origins"""
        static_site = StaticSiteConstruct(
            self,
            "StaticSite",
            domain_name=None,
            certificate_arn=None,
        )

        allowed_origins = list(DEFAULT_ALLOWED_ORIGINS)
        cloudfront_url = f"https://{static_site.distribution.distribution_domain_name}"
        allowed_origins.append(cloudfront_url)

        return allowed_origins

    def _create_storage_infrastructure(
        self, allowed_origins: list[str]
    ) -> S3StorageConstruct:
        """Create S3 storage infrastructure"""
        return S3StorageConstruct(
            self,
            "S3Storage",
            bucket_name=None,
            allowed_origins=allowed_origins,
        )

    def _create_api_infrastructure(
        self, s3_storage: S3StorageConstruct, allowed_origins: list[str]
    ) -> LambdaApiConstruct:
        """Create Lambda API infrastructure"""
        return LambdaApiConstruct(
            self,
            "LambdaApi",
            bucket_name=s3_storage.edf_bucket.bucket_name,
            backend_role=s3_storage.backend_role,
            allowed_origins=allowed_origins,
        )

    def _create_vpc(self) -> ec2.Vpc:
        """Create VPC for ECS Fargate"""
        return ec2.Vpc(
            self,
            "BiormikaVPC",
            max_azs=VPC_MAX_AZS,
            nat_gateways=VPC_NAT_GATEWAYS,
        )

    def _create_processing_infrastructure(
        self,
        vpc: ec2.Vpc,
        sqs: SQSConstruct,
        s3_storage: S3StorageConstruct,
        dynamodb: DynamoDBConstruct,
        ses: SESConstruct,
    ) -> ECSFargateConstruct:
        """Create ECS Fargate infrastructure for HFO processing"""
        return ECSFargateConstruct(
            self,
            "ECSFargate",
            vpc=vpc,
            job_queue=sqs.job_queue,
            s3_bucket=s3_storage.edf_bucket,
            jobs_table=dynamodb.analysis_jobs_table,
            preferences_table=dynamodb.user_preferences_table,
            ses_construct=ses,
        )

    def _configure_permissions(
        self,
        dynamodb: DynamoDBConstruct,
        sqs: SQSConstruct,
        ses: SESConstruct,
        lambda_api: LambdaApiConstruct,
    ) -> None:
        """Configure permissions for Lambda function"""
        dynamodb.grant_read_write(lambda_api.api_function)
        sqs.grant_send_messages(lambda_api.api_function)
        ses.grant_send_email(lambda_api.api_function.role)

    def _configure_lambda_environment(
        self,
        lambda_api: LambdaApiConstruct,
        dynamodb: DynamoDBConstruct,
        sqs: SQSConstruct,
        ses: SESConstruct,
    ) -> None:
        """Configure Lambda environment variables"""
        environment_vars = {
            "JOBS_TABLE_NAME": dynamodb.analysis_jobs_table.table_name,
            "PREFERENCES_TABLE_NAME": dynamodb.user_preferences_table.table_name,
            "SQS_QUEUE_URL": sqs.job_queue.queue_url,
            "SES_SENDER_EMAIL": ses.sender_email,
        }

        for key, value in environment_vars.items():
            lambda_api.api_function.add_environment(key, value)

    def _create_stack_outputs(self, ecs_fargate: ECSFargateConstruct) -> None:
        """Create stack-level outputs"""
        CfnOutput(
            self,
            "ECRRepositoryURI",
            value=ecs_fargate.get_repository_uri(),
            description="ECR repository URI for HFO processor",
        )

    def _apply_tags(self) -> None:
        """Apply tags to all resources in the stack"""
        Tags.of(self).add("Project", PROJECT_NAME)
        Tags.of(self).add("Environment", ENVIRONMENT)
        Tags.of(self).add("Purpose", PURPOSE)
