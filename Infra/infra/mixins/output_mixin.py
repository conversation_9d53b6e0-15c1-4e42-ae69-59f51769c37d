from aws_cdk import CfnOutput
from constructs import Construct


class OutputMixin:
    """Mixin for managing CloudFormation outputs"""

    def __init__(self):
        self._outputs: dict[str, CfnOutput] = {}

    def create_output(
        self,
        id: str,
        value: str,
        description: str,
        export_name: str | None = None,
    ) -> CfnOutput:
        """Create and track CloudFormation outputs"""
        if not isinstance(self, Construct):
            raise TypeError("OutputMixin must be used with a Construct")

        output = CfnOutput(
            self,
            id,
            value=value,
            description=description,
            export_name=export_name,
        )
        self._outputs[id] = output
        return output

    def get_output(self, id: str) -> CfnOutput | None:
        """Get an output by ID"""
        return self._outputs.get(id)

    def list_outputs(self) -> dict[str, CfnOutput]:
        """Get all outputs"""
        return self._outputs.copy()
