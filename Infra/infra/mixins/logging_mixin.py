from aws_cdk import RemovalPolicy
from aws_cdk import aws_logs as logs
from constructs import Construct


class LoggingMixin:
    """Mixin for CloudWatch logging functionality"""

    def create_log_group(
        self,
        id: str,
        log_group_name: str,
        retention_days: logs.RetentionDays = logs.RetentionDays.ONE_WEEK,
        removal_policy: RemovalPolicy = RemovalPolicy.DESTROY,
    ) -> logs.LogGroup:
        """Create CloudWatch log group with standard settings"""
        if not isinstance(self, Construct):
            raise TypeError("LoggingMixin must be used with a Construct")

        return logs.LogGroup(
            self,
            id,
            log_group_name=log_group_name,
            retention=retention_days,
            removal_policy=removal_policy,
        )
