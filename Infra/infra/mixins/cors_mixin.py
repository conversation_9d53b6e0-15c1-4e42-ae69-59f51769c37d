from typing import Any

from aws_cdk import Duration
from aws_cdk import aws_apigateway as apigw
from aws_cdk import aws_s3 as s3


class CorsMixin:
    """Mixin for CORS configuration"""

    def create_s3_cors_rules(self, allowed_origins: list[str]) -> list[s3.CorsRule]:
        """Create S3 bucket CORS rules"""
        from ..config import S3_CORS_EXPOSED_HEADERS, S3_CORS_MAX_AGE_SECONDS

        return [
            s3.CorsRule(
                allowed_methods=[
                    s3.HttpMethods.GET,
                    s3.HttpMethods.PUT,
                    s3.HttpMethods.POST,
                    s3.HttpMethods.HEAD,
                ],
                allowed_origins=allowed_origins,
                allowed_headers=["*"],
                exposed_headers=S3_CORS_EXPOSED_HEADERS,
                max_age=S3_CORS_MAX_AGE_SECONDS,
            )
        ]

    def create_api_cors_options(self, allowed_origins: list[str]) -> dict[str, Any]:
        """Create API Gateway CORS configuration options"""
        from ..config import API_CORS_MAX_AGE_HOURS

        return {
            "allow_origins": [*allowed_origins, "https://*"],
            "allow_methods": apigw.Cors.ALL_METHODS,
            "allow_headers": [
                "Content-Type",
                "X-Amz-Date",
                "Authorization",
                "X-Api-Key",
                "X-Amz-Security-Token",
                "X-Amz-User-Agent",
            ],
            "allow_credentials": True,
            "max_age": Duration.hours(API_CORS_MAX_AGE_HOURS),
        }
