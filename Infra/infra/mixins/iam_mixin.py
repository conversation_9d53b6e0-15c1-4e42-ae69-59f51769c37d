from aws_cdk import aws_iam as iam
from constructs import Construct


class IamMixin:
    """Mixin for IAM-related functionality"""

    def create_service_role(
        self,
        id: str,
        service_principals: list[str],
        description: str,
        managed_policies: list[iam.IManagedPolicy] | None = None,
    ) -> iam.Role:
        """Create IAM role for AWS services"""
        if not isinstance(self, Construct):
            raise TypeError("IamMixin must be used with a Construct")

        principals = [iam.ServicePrincipal(sp) for sp in service_principals]
        role = iam.Role(
            self,
            id,
            assumed_by=iam.CompositePrincipal(*principals)
            if len(principals) > 1
            else principals[0],
            description=description,
        )

        if managed_policies:
            for policy in managed_policies:
                role.add_managed_policy(policy)

        return role

    def grant_s3_permissions(
        self,
        role: iam.IRole,
        bucket_arn: str,
        actions: list[str],
        resource_patterns: list[str] | None = None,
    ) -> iam.PolicyStatement:
        """Grant S3 permissions to a role"""
        resources = [bucket_arn]
        if resource_patterns:
            resources.extend(
                [f"{bucket_arn}/{pattern}" for pattern in resource_patterns]
            )
        else:
            resources.append(f"{bucket_arn}/*")

        statement = iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=actions,
            resources=resources,
        )
        role.add_to_policy(statement)
        return statement

    def create_s3_read_write_policy(
        self, bucket_name: str, resource_prefix: str = ""
    ) -> iam.PolicyStatement:
        """Create IAM policy for S3 read/write operations"""
        from ..config import S3_ACTIONS_READ_WRITE

        resources = [f"arn:aws:s3:::{bucket_name}"]
        if resource_prefix:
            resources.append(f"arn:aws:s3:::{bucket_name}/{resource_prefix}*")
        else:
            resources.append(f"arn:aws:s3:::{bucket_name}/*")

        return iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=S3_ACTIONS_READ_WRITE,
            resources=resources,
        )

    def create_s3_presigned_policy(
        self, bucket_arn: str, allowed_extensions: list[str] | None = None
    ) -> iam.PolicyStatement:
        """Create IAM policy for S3 presigned URL operations"""
        from ..config import S3_ACTIONS_PRESIGNED

        statement = iam.PolicyStatement(
            effect=iam.Effect.ALLOW,
            actions=S3_ACTIONS_PRESIGNED,
            resources=[bucket_arn, f"{bucket_arn}/*"],
        )

        if allowed_extensions:
            statement.add_condition("StringLike", {"s3:prefix": allowed_extensions})

        return statement
