from constructs import Construct

from .mixins import CorsMixin, IamMixin, LoggingMixin, OutputMixin


class BaseConstruct(Construct, OutputMixin, LoggingMixin, IamMixin, CorsMixin):
    """Base class for all CDK constructs with common functionality"""

    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        Construct.__init__(self, scope, construct_id)
        OutputMixin.__init__(self)

    def build_environment_config(self, **kwargs) -> dict[str, str]:
        """Build environment configuration for compute resources"""
        return {k: v for k, v in kwargs.items() if v is not None}
