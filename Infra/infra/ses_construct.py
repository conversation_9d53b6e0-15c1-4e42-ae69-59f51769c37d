# Amazon SES configuration for email notifications
from aws_cdk import aws_iam as iam
from aws_cdk import aws_ses as ses
from constructs import Construct

from .base_construct import BaseConstruct
from .email_templates import (
    ANALYSIS_COMPLETE_TEMPLATE,
    BATCH_COMPLETE_TEMPLATE,
    ERROR_TEMPLATE,
)


class SESConstruct(BaseConstruct):
    def __init__(
        self,
        scope: Construct,
        construct_id: str,
        sender_email: str = "<EMAIL>",
        **kwargs,
    ) -> None:
        super().__init__(scope, construct_id)

        self.sender_email = sender_email

        self._create_configuration_set()
        self._create_email_templates()

    def _create_configuration_set(self) -> None:
        """Create SES configuration set for tracking."""
        self.configuration_set = ses.CfnConfigurationSet(
            self,
            "EmailConfigurationSet",
            name="biormika-notifications",
            reputation_options=ses.CfnConfigurationSet.ReputationOptionsProperty(
                reputation_metrics_enabled=True
            ),
            sending_options=ses.CfnConfigurationSet.SendingOptionsProperty(
                sending_enabled=True
            ),
            suppression_options=ses.CfnConfigurationSet.SuppressionOptionsProperty(
                suppressed_reasons=["COMPLAINT", "BOUNCE"]
            ),
        )

    def _create_email_templates(self) -> None:
        """Create all email templates."""
        self.analysis_complete_template = self._create_template(
            "AnalysisCompleteTemplate", ANALYSIS_COMPLETE_TEMPLATE
        )

        self.batch_complete_template = self._create_template(
            "BatchCompleteTemplate", BATCH_COMPLETE_TEMPLATE
        )

        self.error_template = self._create_template(
            "AnalysisErrorTemplate", ERROR_TEMPLATE
        )

    def _create_template(self, id: str, template_config: dict) -> ses.CfnTemplate:
        """Create an SES email template."""
        return ses.CfnTemplate(
            self,
            id,
            template=ses.CfnTemplate.TemplateProperty(
                template_name=template_config["template_name"],
                subject_part=template_config["subject_part"],
                html_part=template_config["html_part"],
                text_part=template_config["text_part"],
            ),
        )

    def grant_send_email(self, principal: iam.IGrantable) -> None:
        """Grant permission to send emails using SES."""
        principal.add_to_principal_policy(
            iam.PolicyStatement(
                actions=[
                    "ses:SendEmail",
                    "ses:SendTemplatedEmail",
                    "ses:SendBulkTemplatedEmail",
                ],
                resources=["*"],
            )
        )
