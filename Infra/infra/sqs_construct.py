# SQS queues for HFO analysis job processing
from aws_cdk import Duration
from aws_cdk import aws_cloudwatch as cloudwatch
from aws_cdk import aws_iam as iam
from aws_cdk import aws_sqs as sqs
from constructs import Construct

from .base_construct import BaseConstruct


class SQSConstruct(BaseConstruct):
    def __init__(self, scope: Construct, construct_id: str, **kwargs) -> None:
        super().__init__(scope, construct_id)

        self._create_queues()
        self._create_alarms()

    def _create_queues(self) -> None:
        """Create SQS queues with DLQ configuration."""
        # Dead Letter Queue
        self.dlq = sqs.Queue(
            self,
            "AnalysisJobsDLQ",
            queue_name="biormika-analysis-jobs-dlq",
            retention_period=Duration.days(14),
            visibility_timeout=Duration.minutes(5),
        )

        # Main processing queue
        self.job_queue = sqs.Queue(
            self,
            "AnalysisJobsQueue",
            queue_name="biormika-analysis-jobs",
            visibility_timeout=Duration.hours(1),
            retention_period=Duration.days(14),
            dead_letter_queue=sqs.DeadLetterQueue(
                max_receive_count=1,  # Fail-fast: no retries, immediate DLQ on failure
                queue=self.dlq,
            ),
            receive_message_wait_time=Duration.seconds(20),
        )

    def _create_alarms(self) -> None:
        """Create CloudWatch alarms for monitoring."""
        self.high_queue_depth_alarm = cloudwatch.Alarm(
            self,
            "HighQueueDepth",
            metric=self.job_queue.metric_approximate_number_of_messages_visible(),
            threshold=100,
            evaluation_periods=2,
            alarm_description="Alert when job queue has more than 100 messages",
        )

        self.dlq_messages_alarm = cloudwatch.Alarm(
            self,
            "DLQMessages",
            metric=self.dlq.metric_approximate_number_of_messages_visible(),
            threshold=1,
            evaluation_periods=1,
            alarm_description="Alert when messages are in the dead letter queue",
        )

    def grant_send_messages(self, principal: iam.IGrantable) -> None:
        """Grant permission to send messages to the job queue."""
        self.job_queue.grant_send_messages(principal)

    def grant_consume_messages(self, principal: iam.IGrantable) -> None:
        """Grant permission to consume messages from the job queue."""
        self.job_queue.grant_consume_messages(principal)

    def get_queue_metrics(self) -> dict:
        """Return queue metrics for dashboard."""
        queue = self.job_queue
        return {
            "queue_depth": queue.metric_approximate_number_of_messages_visible(),
            "oldest_message_age": queue.metric_approximate_age_of_oldest_message(),
            "dlq_messages": self.dlq.metric_approximate_number_of_messages_visible(),
        }
