"""Reusable patterns for AWS CDK constructs"""

from aws_cdk import Duration, RemovalPolicy
from aws_cdk import aws_cloudwatch as cloudwatch
from aws_cdk import aws_iam as iam
from aws_cdk import aws_lambda as lambda_
from aws_cdk import aws_logs as logs
from aws_cdk import aws_s3 as s3
from aws_cdk import aws_sqs as sqs

from .config import (
    LAMBDA_ARCHITECTURE,
    LAMBDA_LOG_RETENTION_DAYS,
    LAMBDA_MEMORY_SIZE_MB,
    LAMBDA_RUNTIME_PYTHON,
    LAMBDA_TIMEOUT_SECONDS,
    S3_MULTIPART_UPLOAD_CLEANUP_DAYS,
)


class S3Patterns:
    """Standard S3 bucket configurations"""

    @staticmethod
    def create_secure_bucket(
        scope,
        id: str,
        bucket_name: str | None = None,
        versioned: bool = True,
        cors_rules: list[s3.CorsRule] | None = None,
    ) -> s3.Bucket:
        """Create S3 bucket with security best practices"""
        return s3.Bucket(
            scope,
            id,
            bucket_name=bucket_name,
            versioned=versioned,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            lifecycle_rules=[
                s3.LifecycleRule(
                    id="DeleteIncompleteMultipartUploads",
                    abort_incomplete_multipart_upload_after=Duration.days(
                        S3_MULTIPART_UPLOAD_CLEANUP_DAYS
                    ),
                )
            ],
            cors=cors_rules,
            removal_policy=RemovalPolicy.RETAIN,
        )

    @staticmethod
    def create_static_site_bucket(scope, id: str) -> s3.Bucket:
        """Create S3 bucket for static website hosting"""
        return s3.Bucket(
            scope,
            id,
            encryption=s3.BucketEncryption.S3_MANAGED,
            block_public_access=s3.BlockPublicAccess.BLOCK_ALL,
            removal_policy=RemovalPolicy.DESTROY,
            auto_delete_objects=True,
            versioned=False,
        )


class LambdaPatterns:
    """Standard Lambda function configurations"""

    @staticmethod
    def create_python_lambda(
        scope,
        id: str,
        handler: str,
        code: lambda_.Code,
        environment: dict[str, str] | None = None,
        role: iam.IRole | None = None,
    ) -> lambda_.Function:
        """Create Lambda function with standard settings"""
        log_group = logs.LogGroup(
            scope,
            f"{id}LogGroup",
            retention=getattr(
                logs.RetentionDays,
                f"{'ONE_WEEK' if LAMBDA_LOG_RETENTION_DAYS == 7 else 'ONE_MONTH'}",
            ),
            removal_policy=RemovalPolicy.DESTROY,
        )

        return lambda_.Function(
            scope,
            id,
            runtime=getattr(lambda_.Runtime, LAMBDA_RUNTIME_PYTHON),
            architecture=getattr(lambda_.Architecture, LAMBDA_ARCHITECTURE),
            handler=handler,
            code=code,
            timeout=Duration.seconds(LAMBDA_TIMEOUT_SECONDS),
            memory_size=LAMBDA_MEMORY_SIZE_MB,
            role=role,
            environment=environment or {},
            log_group=log_group,
        )


class SQSPatterns:
    """Standard SQS queue configurations"""

    @staticmethod
    def create_queue_with_dlq(
        scope,
        id: str,
        queue_name: str,
        visibility_timeout: Duration,
        max_receive_count: int = 3,
    ) -> tuple[sqs.Queue, sqs.Queue]:
        """Create SQS queue with dead letter queue"""
        # Dead letter queue
        dlq = sqs.Queue(
            scope,
            f"{id}DLQ",
            queue_name=f"{queue_name}-dlq",
            retention_period=Duration.days(14),
            visibility_timeout=Duration.minutes(5),
        )

        # Main queue
        queue = sqs.Queue(
            scope,
            id,
            queue_name=queue_name,
            visibility_timeout=visibility_timeout,
            retention_period=Duration.days(14),
            dead_letter_queue=sqs.DeadLetterQueue(
                max_receive_count=max_receive_count,
                queue=dlq,
            ),
            receive_message_wait_time=Duration.seconds(20),
        )

        return queue, dlq


class AlarmPatterns:
    """Standard CloudWatch alarm configurations"""

    @staticmethod
    def create_queue_depth_alarm(
        scope, id: str, queue: sqs.IQueue, threshold: int
    ) -> cloudwatch.Alarm:
        """Create alarm for SQS queue depth"""
        return cloudwatch.Alarm(
            scope,
            id,
            metric=queue.metric_approximate_number_of_messages_visible(),
            threshold=threshold,
            evaluation_periods=2,
            alarm_description=f"Alert when queue has more than {threshold} messages",
        )

    @staticmethod
    def create_dlq_alarm(scope, id: str, dlq: sqs.IQueue) -> cloudwatch.Alarm:
        """Create alarm for DLQ messages"""
        return cloudwatch.Alarm(
            scope,
            id,
            metric=dlq.metric_approximate_number_of_messages_visible(),
            threshold=1,
            evaluation_periods=1,
            alarm_description="Alert when messages are in the dead letter queue",
        )
