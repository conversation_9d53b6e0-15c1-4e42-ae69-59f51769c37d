"""Service-specific configuration for Lambda, ECS, DynamoDB, etc."""

# Lambda configuration
LAMBDA_RUNTIME_PYTHON = "PYTHON_3_11"
LAMBDA_ARCHITECTURE = "ARM_64"
LAMBDA_TIMEOUT_SECONDS = 30
LAMBDA_MEMORY_SIZE_MB = 512
LAMBDA_LOG_RETENTION_DAYS = 7
LAMBDA_LOG_GROUP_NAME = "/aws/lambda/biormika-api"
LAMBDA_HANDLER = "lambda_handler.handler"

# API Gateway configuration
API_STAGE_NAME = "prod"
API_THROTTLE_RATE_LIMIT = 100
API_THROTTLE_BURST_LIMIT = 200

# ECS configuration
ECS_TASK_CPU = 8192
ECS_TASK_MEMORY_MB = 16384
ECS_MIN_CAPACITY = 1  # Keep warm container
ECS_MAX_CAPACITY = 10
ECS_LOG_RETENTION_DAYS = 7

# DynamoDB tables
DYNAMODB_USER_PREFERENCES_TABLE = "biormika-user-preferences"
DYNAMODB_ANALYSIS_JOBS_TABLE = "biormika-analysis-jobs"

# VPC configuration
VPC_MAX_AZS = 2
VPC_NAT_GATEWAYS = 1
