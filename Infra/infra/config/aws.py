"""AWS account and region configuration"""

import os

# Project metadata
PROJECT_NAME = "Biormika"
PURPOSE = "EDF File Storage and Management"
STACK_NAME = "BiormikaStack"
STACK_DESCRIPTION = "Infrastructure for Biormika EDF file management and HFO analysis"
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")

# AWS configuration
AWS_PROFILE = os.getenv("AWS_PROFILE", "biormika")
AWS_REGION = os.getenv("AWS_REGION", "us-east-1")
DEFAULT_ACCOUNT = os.getenv("CDK_DEFAULT_ACCOUNT", "************")
DEFAULT_REGION = os.getenv("CDK_DEFAULT_REGION", AWS_REGION)

# Email settings
SENDER_EMAIL = os.getenv("SENDER_EMAIL", "<EMAIL>")
