"""Network, CORS, and S3 configuration"""

# Port configuration
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]

API_CORS_MAX_AGE_HOURS = 1
S3_CORS_MAX_AGE_SECONDS = 3600
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]

# S3 IAM actions
S3_BASE_ACTIONS = [
    "s3:GetObject",
    "s3:PutObject",
    "s3:DeleteObject",
    "s3:ListBucket",
]

S3_MULTIPART_ACTIONS = [
    "s3:CreateMultipartUpload",
    "s3:UploadPart",
    "s3:CompleteMultipartUpload",
    "s3:AbortMultipartUpload",
]

S3_ACTIONS_READ_WRITE = [
    *S3_BASE_ACTIONS,
    "s3:GetObjectAttributes",
    "s3:HeadObject",
    *S3_MULTIPART_ACTIONS,
    "s3:ListBucketMultipartUploads",
]

S3_ACTIONS_PRESIGNED = [
    *S3_BASE_ACTIONS,
    *S3_MULTIPART_ACTIONS,
]
