"""Resource limits, timeouts, and file restrictions"""

# File restrictions
ALLOWED_FILE_EXTENSIONS = [".edf", ".EDF"]
ALLOWED_FILE_PATTERNS = [f"*{ext}" for ext in ALLOWED_FILE_EXTENSIONS]
MAX_FILE_SIZE_MB = 1024
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
EDF_FILES_PREFIX = "edf-files"

# S3 lifecycle
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1

# SQS configuration
SQS_VISIBILITY_TIMEOUT_HOURS = 1
SQS_MESSAGE_RETENTION_DAYS = 14
SQS_DLQ_MAX_RECEIVE_COUNT = 3
