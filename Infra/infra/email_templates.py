"""Email Template Configuration"""

from pathlib import Path


def load_template(template_name: str, extension: str) -> str:
    """Load a template file from the templates directory."""
    template_path = Path(__file__).parent / "templates" / f"{template_name}.{extension}"

    if template_path.exists():
        return template_path.read_text()

    # Fallback for development/testing
    return f"{{{{template_missing: {template_name}.{extension}}}}}"


# Template configurations
ANALYSIS_COMPLETE_TEMPLATE = {
    "template_name": "biormika-analysis-complete",
    "subject_part": "HFO Analysis Complete - {{filename}}",
    "html_part": load_template("analysis_complete", "html"),
    "text_part": load_template("analysis_complete", "txt"),
}

BATCH_COMPLETE_TEMPLATE = {
    "template_name": "biormika-batch-complete",
    "subject_part": "Batch HFO Analysis Complete - {{batch_count}} files processed",
    "html_part": load_template("batch_complete", "html"),
    "text_part": load_template("batch_complete", "txt"),
}

ERROR_TEMPLATE = {
    "template_name": "biormika-analysis-error",
    "subject_part": "HFO Analysis Failed - {{filename}}",
    "html_part": load_template("error", "html"),
    "text_part": load_template("error", "txt"),
}
