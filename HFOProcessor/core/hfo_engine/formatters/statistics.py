"""
Statistics Calculation Module
Calculates statistics from HFO detection results
"""

import numpy as np
from typing import Dict, List, Any


class HFOStatisticsCalculator:
    """Calculates statistics from HFO detection results"""

    @staticmethod
    def calculate_statistics(
        results: Dict[str, Any],
        hfo_events: List[Dict],
        eeg_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate statistics from HFO results.

        Args:
            results: Raw HFO detection results
            hfo_events: List of extracted HFO events
            eeg_data: Original EEG data

        Returns:
            Dictionary containing calculated statistics
        """
        # Filter for accepted HFOs only
        accepted_hfos = [e for e in hfo_events if e.get("type") == "accepted"]

        # Calculate basic counts
        total_hfos = len(accepted_hfos)

        # Channel statistics
        channel_labels = results.get("channel_labels", [])
        channel_stats = []

        for idx, label in enumerate(channel_labels):
            ch_hfos = [e for e in accepted_hfos if e.get("channel_index") == idx]
            channel_stats.append({
                "channel": label,
                "hfo_count": len(ch_hfos),
                "average_duration": np.mean([e["duration"] for e in ch_hfos]) if ch_hfos else 0,
            })

        # Overall duration statistics
        duration_analyzed_sec = results.get("datalen_resulted_sec", 0)
        duration_analyzed_min = duration_analyzed_sec / 60 if duration_analyzed_sec else 0

        # HFO rate calculation
        hfo_rate = (total_hfos / duration_analyzed_min) if duration_analyzed_min > 0 else 0

        # Frequency characteristics
        peak_freqs = []
        avg_peak_freq = results.get("avg_peak_freq", [])
        if isinstance(avg_peak_freq, (list, np.ndarray)):
            for ch_freqs in avg_peak_freq:
                if isinstance(ch_freqs, (list, np.ndarray)) and len(ch_freqs) > 0:
                    peak_freqs.extend(ch_freqs)
                elif isinstance(ch_freqs, (int, float)) and ch_freqs > 0:
                    peak_freqs.append(ch_freqs)

        # Duration characteristics
        durations = []
        duration_data = results.get("duration", [])
        if isinstance(duration_data, (list, np.ndarray)):
            for ch_durations in duration_data:
                if isinstance(ch_durations, (list, np.ndarray)) and len(ch_durations) > 0:
                    durations.extend(ch_durations)
                elif isinstance(ch_durations, (int, float)) and ch_durations > 0:
                    durations.append(ch_durations)

        # Amplitude characteristics
        amplitudes = []
        amplitude_data = results.get("avg_my_amp", [])
        if isinstance(amplitude_data, (list, np.ndarray)):
            for ch_amps in amplitude_data:
                if isinstance(ch_amps, (list, np.ndarray)) and len(ch_amps) > 0:
                    amplitudes.extend(ch_amps)
                elif isinstance(ch_amps, (int, float)) and ch_amps > 0:
                    amplitudes.append(ch_amps)

        statistics = {
            "total_hfos": total_hfos,
            "hfo_rate_per_minute": round(hfo_rate, 2),
            "duration_analyzed_seconds": round(duration_analyzed_sec, 2),
            "duration_analyzed_minutes": round(duration_analyzed_min, 2),
            "channels_analyzed": len(channel_labels),
            "channel_statistics": channel_stats,
            "frequency_statistics": {
                "mean": round(np.mean(peak_freqs), 2) if peak_freqs else 0,
                "median": round(np.median(peak_freqs), 2) if peak_freqs else 0,
                "std": round(np.std(peak_freqs), 2) if peak_freqs else 0,
                "min": round(np.min(peak_freqs), 2) if peak_freqs else 0,
                "max": round(np.max(peak_freqs), 2) if peak_freqs else 0,
            } if peak_freqs else {},
            "duration_statistics": {
                "mean_ms": round(np.mean(durations), 2) if durations else 0,
                "median_ms": round(np.median(durations), 2) if durations else 0,
                "std_ms": round(np.std(durations), 2) if durations else 0,
                "min_ms": round(np.min(durations), 2) if durations else 0,
                "max_ms": round(np.max(durations), 2) if durations else 0,
            } if durations else {},
            "amplitude_statistics": {
                "mean": round(np.mean(amplitudes), 2) if amplitudes else 0,
                "median": round(np.median(amplitudes), 2) if amplitudes else 0,
                "std": round(np.std(amplitudes), 2) if amplitudes else 0,
                "min": round(np.min(amplitudes), 2) if amplitudes else 0,
                "max": round(np.max(amplitudes), 2) if amplitudes else 0,
            } if amplitudes else {},
            "event_type_counts": {
                "accepted": len([e for e in hfo_events if e.get("type") == "accepted"]),
                "rejected": len([e for e in hfo_events if e.get("type") == "rejected"]),
                "lfo": len([e for e in hfo_events if e.get("type") == "lfo"]),
                "noise": len([e for e in hfo_events if e.get("type") == "noise"]),
            }
        }

        # Add connectivity statistics if available
        connectivity = results.get("con_fact", [])
        if isinstance(connectivity, np.ndarray) and connectivity.size > 0:
            valid_conn = connectivity[connectivity > 0]
            if valid_conn.size > 0:
                statistics["connectivity_statistics"] = {
                    "mean": round(np.mean(valid_conn), 2),
                    "median": round(np.median(valid_conn), 2),
                    "std": round(np.std(valid_conn), 2),
                    "max": round(np.max(valid_conn), 2),
                }

        return statistics