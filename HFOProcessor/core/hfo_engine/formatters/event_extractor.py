"""
HFO Event Extraction Module
Extracts and processes HFO events from detection results
"""

import numpy as np
from typing import Dict, List, Any


class HFOEventExtractor:
    """Extracts HFO events from raw detection results"""

    @staticmethod
    def extract_events(results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract HFO events from raw results, including both accepted and rejected events.

        Args:
            results: Raw HFO detection results

        Returns:
            List of HFO event dictionaries
        """
        hfo_events = []

        # Extract HFO events
        final_start = results.get("final_start_ind", [])
        final_end = results.get("final_end_ind", [])
        rejected_start = results.get("rejected_start_ind", [])
        rejected_end = results.get("rejected_end_ind", [])
        lfo_start = results.get("lfo_start_ind", [])
        lfo_end = results.get("lfo_end_ind", [])
        noise_start = results.get("noise_start_ind", [])
        noise_end = results.get("noise_end_ind", [])

        channel_labels = results.get("channel_labels", [])
        samp_freq = results.get("samp_freq", 256)

        # Process accepted HFO indices
        hfo_events.extend(
            HFOEventExtractor._process_hfo_indices(
                final_start, final_end, "accepted", channel_labels, samp_freq
            )
        )

        # Process rejected HFO indices
        hfo_events.extend(
            HFOEventExtractor._process_hfo_indices(
                rejected_start, rejected_end, "rejected", channel_labels, samp_freq
            )
        )

        # Process LFO indices
        hfo_events.extend(
            HFOEventExtractor._process_hfo_indices(
                lfo_start, lfo_end, "lfo", channel_labels, samp_freq
            )
        )

        # Process noise indices
        hfo_events.extend(
            HFOEventExtractor._process_hfo_indices(
                noise_start, noise_end, "noise", channel_labels, samp_freq
            )
        )

        return hfo_events

    @staticmethod
    def _process_hfo_indices(
        start_indices,
        end_indices,
        event_type: str,
        channel_labels: List[str],
        samp_freq: float
    ) -> List[Dict[str, Any]]:
        """
        Process HFO indices for a specific event type.

        Args:
            start_indices: Start indices array
            end_indices: End indices array
            event_type: Type of event (accepted/rejected/lfo/noise)
            channel_labels: Channel labels
            samp_freq: Sampling frequency

        Returns:
            List of processed HFO events
        """
        events = []

        if not isinstance(start_indices, (list, np.ndarray)):
            return events

        # Handle both list and array types
        if isinstance(start_indices, list):
            # List format (one list per channel)
            for ch_idx, (ch_starts, ch_ends) in enumerate(zip(start_indices, end_indices)):
                if ch_idx < len(channel_labels):
                    ch_label = channel_labels[ch_idx]
                else:
                    ch_label = f"Channel_{ch_idx+1}"

                if isinstance(ch_starts, (list, np.ndarray)) and isinstance(ch_ends, (list, np.ndarray)):
                    # Convert to numpy arrays for easier processing
                    ch_starts = np.array(ch_starts) if isinstance(ch_starts, list) else ch_starts
                    ch_ends = np.array(ch_ends) if isinstance(ch_ends, list) else ch_ends

                    # Filter out zero indices
                    valid_mask = (ch_starts > 0) & (ch_ends > 0)
                    valid_starts = ch_starts[valid_mask]
                    valid_ends = ch_ends[valid_mask]

                    for start, end in zip(valid_starts, valid_ends):
                        start = int(start)
                        end = int(end)
                        events.append({
                            "channel": ch_label,
                            "channel_index": ch_idx,
                            "start_sample": start,
                            "end_sample": end,
                            "start_time": start / samp_freq,
                            "end_time": end / samp_freq,
                            "duration": (end - start) / samp_freq,
                            "type": event_type
                        })
        else:
            # Array format
            start_indices = np.array(start_indices)
            end_indices = np.array(end_indices)

            if start_indices.ndim == 2:
                # 2D array (channels x events)
                num_channels = start_indices.shape[0]
                for ch_idx in range(num_channels):
                    if ch_idx < len(channel_labels):
                        ch_label = channel_labels[ch_idx]
                    else:
                        ch_label = f"Channel_{ch_idx+1}"

                    # Filter out zero indices
                    valid_mask = (start_indices[ch_idx] > 0) & (end_indices[ch_idx] > 0)
                    valid_starts = start_indices[ch_idx][valid_mask]
                    valid_ends = end_indices[ch_idx][valid_mask]

                    for start, end in zip(valid_starts, valid_ends):
                        start = int(start)
                        end = int(end)
                        events.append({
                            "channel": ch_label,
                            "channel_index": ch_idx,
                            "start_sample": start,
                            "end_sample": end,
                            "start_time": start / samp_freq,
                            "end_time": end / samp_freq,
                            "duration": (end - start) / samp_freq,
                            "type": event_type
                        })

        return events