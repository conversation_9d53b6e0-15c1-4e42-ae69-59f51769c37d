"""
Channel Data Processing Module
Processes channel-specific data from HFO detection results
"""

import numpy as np
from typing import Dict, List, Any


class ChannelDataProcessor:
    """Processes channel-specific data from HFO detection results"""

    @staticmethod
    def extract_channel_data(
        results: Dict[str, Any],
        eeg_data: Dict[str, Any]
    ) -> Dict[str, List[float]]:
        """
        Extract channel-specific data for visualization.

        Args:
            results: Raw HFO detection results
            eeg_data: Original EEG data

        Returns:
            Dictionary containing channel-specific data
        """
        channel_data = {}
        channel_labels = results.get("channel_labels", [])

        # Extract HFO counts per channel
        final_start_ind = results.get("final_start_ind", [])
        if isinstance(final_start_ind, (list, np.ndarray)):
            hfo_counts = []
            for ch_idx in range(len(channel_labels)):
                if ch_idx < len(final_start_ind):
                    ch_starts = final_start_ind[ch_idx]
                    if isinstance(ch_starts, (list, np.ndarray)):
                        if isinstance(ch_starts, np.ndarray):
                            count = np.sum(ch_starts > 0)
                        else:
                            count = sum(1 for s in ch_starts if s > 0)
                        hfo_counts.append(count)
                    else:
                        hfo_counts.append(0)
                else:
                    hfo_counts.append(0)
            channel_data["hfo_counts"] = hfo_counts

        # Extract average characteristics per channel
        characteristics = [
            ("avg_peak_freq", "average_peak_frequency"),
            ("avg_my_amp", "average_amplitude"),
            ("avg_hfo_power", "average_power"),
            ("avg_duration", "average_duration"),
            ("con_fact", "connectivity_factor"),
            ("con_factN", "normalized_connectivity"),
        ]

        for result_key, output_key in characteristics:
            data = results.get(result_key)
            if data is not None:
                processed = ChannelDataProcessor._process_channel_array(data, len(channel_labels))
                if processed:
                    channel_data[output_key] = processed

        # Extract channel metadata
        channel_data["channel_labels"] = channel_labels
        channel_data["num_channels"] = len(channel_labels)

        # Extract time series data if needed
        if "myEEG" in results:
            channel_data["signal_data"] = ChannelDataProcessor._extract_signal_data(
                results["myEEG"], channel_labels
            )

        return channel_data

    @staticmethod
    def _process_channel_array(
        data: Any,
        num_channels: int
    ) -> List[float]:
        """
        Process channel-specific array data.

        Args:
            data: Raw channel data (array or list)
            num_channels: Expected number of channels

        Returns:
            List of processed values per channel
        """
        processed = []

        if isinstance(data, np.ndarray):
            if data.ndim == 1:
                # 1D array - one value per channel
                processed = data[:num_channels].tolist()
            elif data.ndim == 2:
                # 2D array - aggregate per channel
                for ch_idx in range(min(data.shape[0], num_channels)):
                    ch_data = data[ch_idx, :]
                    valid_data = ch_data[ch_data > 0]
                    if valid_data.size > 0:
                        processed.append(float(np.mean(valid_data)))
                    else:
                        processed.append(0.0)
        elif isinstance(data, list):
            for ch_idx in range(min(len(data), num_channels)):
                ch_data = data[ch_idx]
                if isinstance(ch_data, (list, np.ndarray)):
                    if isinstance(ch_data, np.ndarray):
                        valid_data = ch_data[ch_data > 0]
                        if valid_data.size > 0:
                            processed.append(float(np.mean(valid_data)))
                        else:
                            processed.append(0.0)
                    else:
                        valid_data = [v for v in ch_data if v > 0]
                        if valid_data:
                            processed.append(float(np.mean(valid_data)))
                        else:
                            processed.append(0.0)
                elif isinstance(ch_data, (int, float)):
                    processed.append(float(ch_data))
                else:
                    processed.append(0.0)

        # Pad with zeros if needed
        while len(processed) < num_channels:
            processed.append(0.0)

        return processed

    @staticmethod
    def _extract_signal_data(
        signal_data: Any,
        channel_labels: List[str]
    ) -> Dict[str, List[float]]:
        """
        Extract signal time series data.

        Args:
            signal_data: Raw signal data
            channel_labels: Channel labels

        Returns:
            Dictionary with signal data per channel
        """
        signal_dict = {}

        if isinstance(signal_data, np.ndarray):
            if signal_data.ndim == 2:
                # 2D array (channels x samples)
                for ch_idx, label in enumerate(channel_labels):
                    if ch_idx < signal_data.shape[0]:
                        # Downsample if too large (>10000 points)
                        ch_signal = signal_data[ch_idx, :]
                        if ch_signal.size > 10000:
                            # Downsample by factor
                            factor = ch_signal.size // 10000
                            ch_signal = ch_signal[::factor]
                        signal_dict[label] = ch_signal.tolist()

        return signal_dict