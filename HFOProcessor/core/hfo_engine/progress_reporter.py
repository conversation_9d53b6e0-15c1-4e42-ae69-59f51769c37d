"""
Progress reporting module for HFO detection.
Handles user feedback and progress updates during analysis.
"""

import logging
from typing import Optional, Callable

logger = logging.getLogger(__name__)


class ProgressReporter:
    """Handles progress reporting during HFO detection"""
    
    def __init__(self, gui_output: Optional[Callable] = None):
        """
        Initialize the progress reporter.
        
        Args:
            gui_output: Optional callback function for GUI output
        """
        self.gui_output = gui_output
    
    def report(self, message: str, newline: bool = True):
        """
        Report a progress message.
        
        Args:
            message: The message to report
            newline: Whether to add a newline after the message
        """
        if newline:
            message = message if message.endswith('\n') else message + '\n'
        
        if self.gui_output:
            self.gui_output(message)
        else:
            logger.info(message.rstrip())
    
    def report_step(self, step_name: str, status: str = ""):
        """
        Report a specific step in the analysis.
        
        Args:
            step_name: Name of the current step
            status: Status message (e.g., "done", "in progress")
        """
        if status:
            self.report(f"{step_name}...{status}")
        else:
            self.report(f"{step_name}...")
    
    def report_channel_info(self, num_channels: int, data_length: int, 
                           duration_sec: float, start_time: float, 
                           end_time: float, duration_min: float):
        """
        Report channel and data information.
        
        Args:
            num_channels: Number of channels to analyze
            data_length: Length of data in points
            duration_sec: Duration in seconds
            start_time: Start time in seconds
            end_time: End time in seconds
            duration_min: Duration in minutes
        """
        info_msg = (
            f"Number of channels that will be analyzed: {num_channels}\n"
            f"Length of data that will be analyzed: {data_length} points; "
            f"{duration_sec:.4f} sec (between {start_time:.4f} and {end_time:.4f} sec time intervals); "
            f"{duration_min:.4f} min\n"
        )
        self.report(info_msg)
    
    def report_notch_filtering(self, frequencies: list):
        """
        Report notch filtering progress.
        
        Args:
            frequencies: List of frequencies being removed
        """
        self.report("Performing notch filtering...")
        self.report("removing ")
        for freq in frequencies:
            self.report(f"{freq} ")
        self.report("Hz...done.\n")
    
    def report_error(self, error_msg: str):
        """
        Report an error message.
        
        Args:
            error_msg: The error message to report
        """
        self.report(f"ERROR: {error_msg}")
    
    def report_warning(self, warning_msg: str):
        """
        Report a warning message.
        
        Args:
            warning_msg: The warning message to report
        """
        self.report(f"WARNING: {warning_msg}")