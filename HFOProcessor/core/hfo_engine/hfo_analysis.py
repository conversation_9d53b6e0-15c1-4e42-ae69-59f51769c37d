
import numpy as np
import os
import re
from datetime import datetime, timedelta
from scipy.signal import butter, filtfilt, hilbert, find_peaks
from .find_blanks import find_blanks
from .hfo_characteristics import calculate_HFO_characteristics
from .hfo_proximity import is_hfo_nearby
from .processing.filters import create_notch_filter
from core.constants import BLANK_DATA_MARKER, DISCONTINUITY_LENGTH_MS


def run_hfo_algorithm(EEG, input_file_path, analysis_start, analysis_end, montage, user_ref, locutoff, hicutoff, gui_output, threshold_option1, threshold_option2, threshold_option3, threshold_option4, threshold_option5, threshold_option6, threshold_option7):
    """
    Run the HFO detection algorithm on EEG data.

    Args:
        EEG (dict): Dictionary containing EEG data, channel locations, sample rate, etc.
        input_file_path (str): Path to the EDF file being analyzed.
        analysis_start (float): The start time of the analysis (in seconds).
        analysis_end (float): The end time of the analysis (in seconds).
        montage (str): The selected montage (e.g., "Bipolar montage", "Average montage").
        user_ref (str): Reference information if referential montage is selected.
        locutoff (float): The low cutoff frequency for filtering.
        hicutoff (float): The high cutoff frequency for filtering.
        feedback_fn (function): A callback function to send feedback to the UI.
        threshold_scheme: % which thresholding scheme is used; 
            (1=meanRMS*thresh; 2=meanRMS+thresh*stdRMS; 3= meanRMS w/outer Tukey; 
            4=meanRMS*(my variable threshold); 5=Hilbert w/ outerTukey; 6=line_length; 
            7=meanRMS+(my variable threshold)*stdRMS ; 8=Hilbert w/ innerTukey; 9=meanRMS w/ inner Tukey; 
            10=meanHilbert+thresh*stdHilbert)

    Returns:
        dict: A dictionary with the result of the HFO analysis (e.g., success, details).
    """

    result = {'success': False}  # Initialize result as a failure by default
    
    result_start = 0  # Starting time point (seconds) for result output with respect to analysis_start
    result_end = -1   # Ending time point (seconds) for result output with respect to analysis_start
    
    visual_discont = []  # Time (in seconds) where EEG discontinuities happen
    discont_length = DISCONTINUITY_LENGTH_MS  # Length of section (in milliseconds) to remove at all user-defined discontinuities
    
    patient = 'file'
    Echan = EEG['nbchan']
    
    # Filtering and noise handling configuration
    filter_on = 1
    notch_filter_on = 1
    noise_freq_removal = 0
    noise_freq_vector = []
    filter_CFA_on = 1
    remove_discont = 1
    reject_blanks = 0
    thresh_scheme = 10

    # Thresholding parameters
    thresh = threshold_option1
    thresh2 = threshold_option2
    min_HFO_len = threshold_option5
    num_peaks = threshold_option3
    num_peaks2 = threshold_option4
    num_peaks3 = 4
    win_len = 5
    min_break = threshold_option6
    con_delay = threshold_option7
    pwr_thresh = 1
    
    # Extract EEG data parameters
    myEEGoriginal = EEG['data']
    orig_num_channels = EEG['nbchan']
    samp_freq = EEG['srate']
    orig_num_pts = EEG['data'].shape[1]
    orig_channel_labels = EEG['chanlocs']
    use_as_ref = user_ref
    MAX_FREQ = round(samp_freq / 3)
    HFO_FREQ = 70
    
    # Feedback output
    if gui_output:
        gui_output(f"\nAnalyzing file... {input_file_path}")
        gui_output(f"Sampling rate: {samp_freq}")
        gui_output(f"Total number of channels: {orig_num_channels}")
        gui_output(f"labels: {orig_channel_labels}")
        gui_output(f"Total length of data: {orig_num_pts} points; {orig_num_pts / samp_freq:.4f} sec; {orig_num_pts / samp_freq / 60:.4f} min")


    # Convert time from seconds to sample points
    my_time_start = round(analysis_start * samp_freq)
    if analysis_end == -1 or analysis_end == 0:
        my_time_end = orig_num_pts
    else:
        my_time_end = round(analysis_end * samp_freq)
    
    if analysis_start < 0:
        if gui_output:
            gui_output("ERROR: Impossible file analysis parameter... analysis_start (<0)... exiting.")
        return result
    
    if analysis_start == 0:
        my_time_start = 1
    
    if my_time_end > orig_num_pts:
        my_time_end = orig_num_pts
    



    my_result_start = round(result_start * samp_freq)
    my_result_end = round(result_end * samp_freq) if result_end != -1 else my_time_end

    
    if result_start < 0:
        if gui_output:
            gui_output("ERROR: Impossible file analysis parameter... result_start (<0)... exiting.")
        return result
    
    if result_start == 0:
        my_result_start = my_time_start
    
    if my_result_end > my_time_end:
        my_result_end = my_time_end
    

    myEEG = myEEGoriginal[:, my_time_start-1:my_time_end]


    if myEEG.shape[1] < 10:
        if gui_output:
            gui_output(f"ERROR: Insufficient data for analysis. Signal has only {myEEG.shape[1]} samples, need at least 10.")
        result['error'] = f"Insufficient data: {myEEG.shape[1]} samples"
        return result






    datalen_resulted_sec = (my_result_end - my_result_start) / samp_freq
    datalen_resulted_min = datalen_resulted_sec / 60
    
    # Find blank sections (discontinuities) in the data
    blank_start, blank_end, _ = find_blanks(myEEG, orig_channel_labels)


    blank_start = np.sort(np.concatenate([blank_start, np.round(samp_freq * np.array(visual_discont))]))
    blank_end = np.sort(np.concatenate([blank_end, np.round(samp_freq * np.array(visual_discont) + discont_length)]))


    blank_start_new = blank_start.copy()
    blank_end_new = blank_end.copy()


    win_len2 = round(win_len * samp_freq / 1000)

    # Apply montage configuration

    channel_labels = []
    myEEGmontage = []

    if montage == 'Bipolar':

        ct = 0
        for i, channel1 in enumerate(EEG['chanlocs'][:]) :  # Exclude the last channel (since it has no pair)
            if str(channel1) != 'REF':
                channel1 = str(channel1)
                

                channel2 = re.sub(r'(\d+)', lambda x: str(int(x.group()) + 1), channel1)
                

                index1 = np.where(np.isin(orig_channel_labels, channel1))[0]
                index2 = np.where(np.isin(orig_channel_labels, channel2))[0]


                if len(index1) >= 1 and len(index2) >= 1:

                    myEEGmontage.append(myEEG[index1[0], :] - myEEG[index2[0], :])

                    channel_labels.append(f'{channel1}-{channel2}')
                    ct += 1

    elif montage == 'Average':

        myEEGaverage_all = np.mean(myEEG, axis=0)


        myEEGuser = myEEG
        myEEGaverage_user = np.mean(myEEGuser, axis=0)


        myEEGaverage = myEEGaverage_user

        for i, channel in enumerate(EEG['chanlocs']):
            myEEGmontage[i, :] = myEEG[i, :] - myEEGaverage
            channel_labels.append(f'{channel}-AVG')

    elif montage == 'Referential':
        if user_ref is not None:
            index1 = np.where(np.isin(orig_channel_labels, user_ref))[0]
            if len(index1) >= 1:
                myEEGaverage = myEEG[index1[0], :]
            else:
                if gui_output:
                    gui_output(f'Reference: User-defined reference channel {user_ref} could not be found')
                else:
                    return None, None
            use_as_ref = user_ref
        else:

            tmp_min = 100000
            tmp_i = -1
            for i, channel in enumerate(EEG['chanlocs']):
                poss_ref = myEEG[i, :]
                poss_ref_mean = np.mean(poss_ref)
                poss_ref_sort = np.sort(poss_ref)
                p25 = int(0.25 * len(poss_ref_sort))
                p75 = int(0.75 * len(poss_ref_sort))

                if gui_output:
                    gui_output(f'Channel: {channel}, mean= {poss_ref_mean}, p25 = {poss_ref_sort[p25]}, p75 = {poss_ref_sort[p75]}, window = {poss_ref_sort[p75] - poss_ref_sort[p25]}')

                if abs(poss_ref_mean) < abs(tmp_min):
                    tmp_min = poss_ref_mean
                    tmp_i = i

            use_as_ref = EEG['chanlocs'][tmp_i]
            index1 = np.where(np.isin(EEG['chanlocs'], use_as_ref))[0]
            myEEGaverage = myEEG[index1[0], :]

        for i, channel in enumerate(EEG['chanlocs']):
            myEEGmontage[i, :] = myEEG[i, :] - myEEGaverage
            channel_labels.append(f'{channel}-{use_as_ref}')

    else:
        if gui_output:
            gui_output('ERROR: Unknown montage\n')
        else:
            myEEGmontage = np.zeros_like(myEEG)


    myEEGmontage = np.array(myEEGmontage)
    myEEGmontage = -1 * myEEGmontage

    myEEG = np.copy(myEEGmontage)


    num_channels, num_pts = myEEG.shape
        
    start_channel = 1
    end_channel = num_channels
    Echan = num_channels
    Schan = 1

    if Echan == -1:
        Echan = end_channel
    
    # Provide feedback on the number of channels
    if gui_output:
        gui_output(f'Number of channels that will be analyzed: {num_channels}')

    # Provide feedback on the length of data that will be analyzed
    data_length_points = my_result_end - my_result_start
    data_length_seconds = data_length_points / samp_freq
    time_interval_start = my_result_start / samp_freq
    time_interval_end = my_result_end / samp_freq
    data_length_minutes = data_length_seconds / 60

    if gui_output:
        gui_output(f'Length of data that will be analyzed: {data_length_points} points; {data_length_seconds:.4f} sec '
                   f'(between {time_interval_start:.4f} and {time_interval_end:.4f} sec time intervals); '
                   f'{data_length_minutes:.4f} min')

    # Notch filtering at 60Hz and harmonics
    if samp_freq <= 2048:
        notch_freq_vector = np.arange(60, samp_freq / 2, 60)
    else:
        if gui_output:
            gui_output("Sampling rate is too high... exiting")
        return
    

    if notch_filter_on == 1:
        if gui_output:
            gui_output("\nPerforming notch filtering...")
        
        if gui_output:
            gui_output("removing ")
        
        for notch_freq in notch_freq_vector:
            Q_factor = 35
            wo = notch_freq / (samp_freq / 2)
            bw = wo / Q_factor
            
            b, a = create_notch_filter(wo, bw)
            for j in range(start_channel - 1, end_channel):

                if myEEG[j, :].size >= 10:
                    myEEG[j, :] = filtfilt(b, a, myEEG[j, :])
                elif gui_output:
                    gui_output(f"Warning: Channel {j+1} has insufficient samples for notch filtering, skipping.")

            if gui_output:
                gui_output(f"{notch_freq} ")
        
        if gui_output:
            gui_output("Hz...done.\n")


    # Bandpass filtering for HFO detection
    if filter_on == 1:
        if gui_output:
            gui_output("\nPerforming filtering to detect HFOs...")


        b, a = butter(6, [locutoff / (samp_freq / 2), hicutoff / (samp_freq / 2)], btype='band')


        for j in range(start_channel - 1, end_channel):

            if myEEG[j, :].size >= 10:
                myEEG[j, :] = filtfilt(b, a, myEEG[j, :])
            elif gui_output:
                gui_output(f"Warning: Channel {j+1} has insufficient samples for bandpass filtering, skipping.")

        if gui_output:
            gui_output("done.\n")

    # CFA filtering (1-70 Hz)
    if filter_CFA_on == 1:
        notch_freq = 60
        Q_factor = 35
        wo = notch_freq / (samp_freq / 2)
        bw = wo / Q_factor


        b_notch, a_notch = create_notch_filter(wo, bw)


        b_bandpass, a_bandpass = butter(4, [1 / (samp_freq / 2), 70 / (samp_freq / 2)], btype='band')


        myEEGcfa = np.zeros_like(myEEG)
        for j in range(start_channel - 1, end_channel):

            if myEEG[j, :].size >= 10:
                myEEGcfa[j, :] = filtfilt(b_notch, a_notch, myEEG[j, :])
                myEEGcfa[j, :] = filtfilt(b_bandpass, a_bandpass, myEEGcfa[j, :])
            else:
                myEEGcfa[j, :] = myEEG[j, :]





    # Remove blank/discontinuous sections

    if remove_discont == 1:
        if gui_output:
            gui_output("\nRemoving blank/discontinuous data...")


        myEEG2 = []
        blank_start_new = []
        cum_length = 0


        for i in range(len(blank_start)):
            tmplen = (blank_end[i] - blank_start[i] + 1)
            blank_start[i] = blank_start[i] - tmplen
            if blank_start[i] < 0:
                blank_start[i] = 1
            blank_end[i] = blank_end[i] + tmplen
            if blank_end[i] > len(myEEG[0]):
                blank_end[i] = len(myEEG[0])

            if len(myEEG[0]) >= blank_end[i]:
                start_idx = int(blank_start[i])
                end_idx = int(blank_end[i])

                myEEG[:, start_idx:end_idx] = BLANK_DATA_MARKER

                blank_start_new.append(start_idx - cum_length)
                cum_length += (end_idx - start_idx + 1)
            else:
                break



        if blank_start.any():
            for i in range(min(start_channel - 1, myEEG.shape[0]), min(end_channel, myEEG.shape[0])):
                tmpd = myEEG[i, :]

                tmpd = np.delete(tmpd, np.where(tmpd == BLANK_DATA_MARKER))
                myEEG2.append(tmpd)

            myEEG = np.array(myEEG2)
            del myEEG2

        num_pts = myEEG.shape[1]
        t = np.arange(num_pts)

        if gui_output:
            gui_output("done.\n")

    # Energy-based HFO detection


    # Initialize energy statistics
    meanSignalRect = np.zeros(num_channels)
    stdSignalRect = np.zeros(num_channels)
    RMS2 = np.zeros((num_channels, num_pts - (win_len2 - 1)))
    stdHilbert = np.zeros(num_channels)
    meanHilbert = np.zeros(num_channels)

    # Calculate energy statistics
    if gui_output:
        gui_output('\nCalculating energy statistics of the signal...')
        gui_output('Rectifying signal...')

    for j in range(start_channel - 1, end_channel):
        tmpd = myEEG[j, :]
        tmpd_rec = np.abs(tmpd)

        stdSignalRect[j] = np.std(tmpd_rec)
        meanSignalRect[j] = np.mean(tmpd_rec)

    if gui_output:
        gui_output('Rectification complete.')



    # Hilbert transform for energy envelope
    for j in range(start_channel - 1, end_channel):
        RMS2[j, :] = np.abs(hilbert(myEEG[j, :num_pts - (win_len2 - 1)]))

        tmpd = RMS2[j, :]
        stdHilbert[j] = np.std(tmpd)
        meanHilbert[j] = np.mean(tmpd)

    if gui_output:
        gui_output('Energy statistics calculation done.')




    if gui_output:
        gui_output('Finding HFOs... ')

    # Find HFO segments


    start_ind = [[] for _ in range(num_channels)]
    end_ind = [[] for _ in range(num_channels)]

    for j in range(start_channel - 1, end_channel):
        if thresh_scheme != 10:
            return
        
        tmpd = RMS2[j, :]
        tmpm = meanHilbert[j] + thresh * stdHilbert[j]

        flag = 0
        ct = 0


        for i in range(num_pts - (win_len2 - 1)):
            if tmpd[i] > tmpm:
                flag = 1
                ct += 1
            else:
                if flag == 1:
                    if ct >= min_HFO_len * samp_freq / 1000:
                        start_ind[j].append(i - ct)
                        end_ind[j].append(i - 1)
                    flag = 0
                    ct = 0


        if flag == 1 and ct >= min_HFO_len * samp_freq / 1000:
            start_ind[j].append(i - ct)  # Store the final start index
            end_ind[j].append(i)  # Store the final end index

    # Convert lists to numpy arrays and pad with zeros if necessary
    max_len = max(len(lst) for lst in start_ind)
    start_ind = np.array([lst + [0] * (max_len - len(lst)) for lst in start_ind], dtype=int)
    end_ind = np.array([lst + [0] * (max_len - len(lst)) for lst in end_ind], dtype=int)



    # Recalculate number of putative HFOs after applying the criteria

    # Initialize the total HFO count
    total_sum = 0

    # Iterate through each channel
    for j in range(start_channel - 1, end_channel):
        # Find indices where HFO starts are greater than 0
        tmpa = np.where(start_ind[j, :] > 0)[0]
        
        if tmpa.size > 0:
            total_sum += len(tmpa)
            output_message = f'Channel {j + 1}:\t{len(tmpa)}; Total {total_sum}\n'
            

        else:
            pass  # No HFOs detected for this channel

    # Putative HFOs that are separated by less than min_break millisec are combined into a single event (removed indices are replaced by zeros)

    _, c = start_ind.shape  # Get the number of columns (c) in start_ind

    # Loop through each channel to merge close-proximity HFOs
    for j in range(start_channel - 1, end_channel):  # Adjust for 0-based indexing
        si = list(start_ind[j, :])
        ei = list(end_ind[j, :])
        i = 0
        
        # Merge HFOs that are within min_break distance
        while i < len(si) - 1:
            if si[i + 1] != 0:
                if si[i + 1] < ei[i] + min_break * samp_freq / 1000:
                    ei[i] = ei[i + 1]
                    del si[i + 1]  # Remove element from start indices
                    del ei[i + 1]  # Remove element from end indices
                else:
                    i += 1
            else:
                break

        # Ensure the length of si and ei matches the original size by padding with zeros
        pad = len(si) - c
        if pad < 0:
            si.extend([0] * abs(pad))  # Pad with zeros if necessary
            ei.extend([0] * abs(pad))

        # Update start_ind and end_ind with the modified si and ei
        start_ind[j, :] = si[:c]  # Slice to ensure the size matches the original
        end_ind[j, :] = ei[:c]

    # After combining HFOs in close proximity

    total_sum = 0
    for j in range(start_channel - 1, end_channel):
        tmpa = np.where(start_ind[j, :] > 0)[0]
        
        if tmpa.size > 0:
            total_sum += len(tmpa)
            output_message = f'Channel {j + 1}:\t{len(tmpa)}; Total {total_sum}\n'
            
            # if gui_output:
            #     gui_output(output_message)
            # else:
        else:
            # Add debug information in case there are no HFOs
            debug_message = f'Channel {j + 1}: No HFOs detected\n'
            
            # if gui_output:
            #     gui_output(debug_message)
            # else:



    # Initialize rejected indices arrays for channels with too few peaks
    rejected_start_ind = np.zeros((num_channels, c))  # Rejected HFOs (too few peaks)
    rejected_end_ind = np.zeros((num_channels, c))  
    rejecLONG_start_ind = np.zeros((num_channels, c))  # Rejected HFOs with too few peaks but longer
    rejecLONG_end_ind = np.zeros((num_channels, c))  

    # Loop through each channel to check peak requirements for HFOs
    for j in range(start_channel - 1, end_channel):  # Adjust for 0-based indexing
        i = 0
        ctt = 0
        ctt2 = 0
        si = list(start_ind[j, :])
        ei = list(end_ind[j, :])

        while i < len(si) and si[i] != 0:
            tmphfo = myEEG[j, si[i]:ei[i]]

            # Rectify the signal
            tmphfo_rec = np.abs(tmphfo)

            # Find local maxima (peaks) in the rectified signal
            peaks, _ = find_peaks(tmphfo_rec)
            
            # Find peaks above the threshold
            threshold = meanSignalRect[j] + thresh2 * stdSignalRect[j]
            tmpa = np.where(peaks > threshold)[0]

            # Reject HFOs that don't meet the peak requirements
            if len(peaks) < num_peaks or len(tmpa) < num_peaks2: # because it has too few large peaks
                if len(peaks) >= num_peaks3:
                    rejecLONG_start_ind[j, ctt] = si[i]
                    rejecLONG_end_ind[j, ctt] = ei[i]
                    ctt += 1
                else:
                    rejected_start_ind[j, ctt2] = si[i]
                    rejected_end_ind[j, ctt2] = ei[i]
                    ctt2 += 1
                # Remove HFO from indices list
                del si[i]
                del ei[i]
            else:
                i += 1

        # Keep the length of si and ei the same as the original by padding with zeros
        pad = len(si) - c
        if pad < 0:
            si.extend([0] * abs(pad))  # Pad with zeros if necessary
            ei.extend([0] * abs(pad))

        # Update start_ind and end_ind arrays with the modified values
        start_ind[j, :] = si[:c]  # Ensure correct slicing to maintain original size
        end_ind[j, :] = ei[:c]

    # After rejecting HFOs that fail the peaks criteria

    total_sum = 0
    for j in range(start_channel - 1, end_channel):
        tmpa = np.where(start_ind[j, :] > 0)[0]
        
        if tmpa.size > 0:
            total_sum += len(tmpa)
            output_message = f'Channel {j + 1}:\t{len(tmpa)}; Total {total_sum}\n'
            
            # if gui_output:
            #     gui_output(output_message)
            # else:
        else:
            # Add debug information in case there are no HFOs
            debug_message = f'Channel {j + 1}: No HFOs detected\n'
            
            # if gui_output:
            #     gui_output(debug_message)
            # else:


    ######## Reject HFOs that occur near blank periods - these HFOs are artifacts from discontinuity of the data
    ####################################################################

    def near_blank_period(blank_starts, blank_ends, hfo_start, hfo_end, margin=10):
        """
        Check if an HFO occurs near a blank/discontinuous period

        Args:
            blank_starts: Array of blank period start indices
            blank_ends: Array of blank period end indices
            hfo_start: Start index of the HFO
            hfo_end: End index of the HFO
            margin: Number of samples to consider as "near" a blank period

        Returns:
            bool: True if HFO is near a blank period, False otherwise
        """
        margin_samples = margin * samp_freq / 1000  # Convert margin from ms to samples

        for i in range(len(blank_starts)):
            # Check if HFO overlaps with or is near this blank period
            blank_start = blank_starts[i] - margin_samples
            blank_end = blank_ends[i] + margin_samples if i < len(blank_ends) else blank_starts[i] + margin_samples

            # Check for any overlap
            if not (hfo_end < blank_start or hfo_start > blank_end):
                return True

        return False

    if reject_blanks == 1:
        r, c = start_ind.shape  # Get the size of the start_ind matrix
        for j in range(start_channel - 1, end_channel):  # Adjust for 0-based indexing
            i = 0
            si = list(start_ind[j, :])
            ei = list(end_ind[j, :])

            while i < len(si) and si[i] != 0:
                if near_blank_period(blank_start_new, blank_end_new, si[i], ei[i]):
                    # Remove HFO from indices list
                    del si[i]
                    del ei[i]
                else:
                    i += 1

            pad = len(si) - c  # Keep the length of si and ei the same as the original
            if pad < 0:
                si.extend([0] * abs(pad))  # Pad with zeros if necessary
                ei.extend([0] * abs(pad))

            # Update start_ind and end_ind
            start_ind[j, :] = np.array(si)
            end_ind[j, :] = np.array(ei)

    # Recalculate the number of HFOs after removing those near blank periods

    total_sum = 0
    for j in range(start_channel - 1, end_channel):  # Adjust for 0-based indexing
        tmpa = np.where(start_ind[j, :] > 0)[0]
        if tmpa.size > 0:
            total_sum += len(tmpa)
            # if gui_output:
            #     gui_output(f'Channel {j + 1}:\t{len(tmpa)}; Total {total_sum}\n')
            # else:


    if gui_output:
        gui_output('...done.\n')

    # Calculate HFO characteristics (power, frequency, duration, etc.)
    if gui_output:
        gui_output('\nCalculating HFO characteristics...')

    # Initialize variables as lists for dynamic resizing
    num_min = num_pts / samp_freq / 60  # Number of minutes analyzed
    counter = np.zeros(num_channels)  # Number of HFOs for each channel
    duration = [[] for _ in range(num_channels)]  # HFO durations for each channel (list)
    hfo_power = [[] for _ in range(num_channels)]  # Power in the HFO frequency range (list)
    peak_freq = [[] for _ in range(num_channels)]  # Peak frequency from FFT (list)
    my_amp = [[] for _ in range(num_channels)]  # Mean amplitude of rectified signal (list)
    max_freq = [[] for _ in range(num_channels)]  # Maximum frequency (list)
    density = np.zeros(num_channels)  # (Sum of durations of each detected HFO) / (number of sec of data analyzed)
    avg_duration = np.zeros(num_channels)  # Average duration for each channel
    avg_hfo_power = np.zeros(num_channels)  # Average HFO power for each channel
    avg_peak_freq = np.zeros(num_channels)  # Average peak frequency
    avg_my_amp = np.zeros(num_channels)  # Average amplitude of rectified signal
    avg_max_freq = np.zeros(num_channels)  # Average max frequency

    # Rejected HFO counts
    rejected = 0  # Number of rejected HFOs (low frequency)
    noise_rejected = 0  # Number of rejected HFOs (too close to noise freq)

    # Start and end indices for final and rejected HFOs (use lists for dynamic resizing)
    final_start_ind = [[] for _ in range(num_channels)]
    final_end_ind = [[] for _ in range(num_channels)]
    lfo_start_ind = [[] for _ in range(num_channels)]  # Start and stop indices for low-frequency oscillations
    lfo_end_ind = [[] for _ in range(num_channels)]
    noise_start_ind = [[] for _ in range(num_channels)]  # Start and stop indices for noise-frequency oscillations
    noise_end_ind = [[] for _ in range(num_channels)]

    # Loop through each channel to calculate HFO characteristics
    my_result_end = my_result_end - my_result_start 
    my_result_start = 0
    for j in range(start_channel - 1, end_channel):  # Adjust for 0-based indexing
        i = 0
        while i < len(start_ind[j, :]) and start_ind[j, i] != 0:

            if my_result_start <= start_ind[j, i] <= my_result_end:  # Only consider HFOs within the result window
                tmpd = myEEG[j, start_ind[j, i]:end_ind[j, i]]
                # Call calculate_HFO_characteristics function to calculate HFO characteristics
                flag, td, tpp, tphfo, tpf, ttp, tmf = calculate_HFO_characteristics(tmpd, samp_freq, noise_freq_removal, noise_freq_vector, HFO_FREQ, MAX_FREQ, pwr_thresh)
                if flag == 1:  # True HFO
                    counter[j] += 1
                    duration[j].append(td)  # Append to list
                    hfo_power[j].append(tphfo)  # Append to list
                    peak_freq[j].append(tpf)  # Append to list
                    my_amp[j].append(ttp)  # Append to list
                    max_freq[j].append(tmf)  # Append to list
                    density[j] += td  # Add all HFO durations
                    final_start_ind[j].append(start_ind[j, i])  # Append to list
                    final_end_ind[j].append(end_ind[j, i])  # Append to list
                elif flag == 0:  # Rejected due to low frequency
                    rejected += 1
                    lfo_start_ind[j].append(start_ind[j, i])  # Append to list
                    lfo_end_ind[j].append(end_ind[j, i])  # Append to list
                else:  # Rejected due to noise
                    noise_rejected += 1
                    noise_start_ind[j].append(start_ind[j, i])  # Append to list
                    noise_end_ind[j].append(end_ind[j, i])  # Append to list
            i += 1

        # Convert lists to numpy arrays for further calculations
        if duration[j]:
            avg_duration[j] = np.mean(duration[j])
        if hfo_power[j]:
            avg_hfo_power[j] = np.mean(np.log10(hfo_power[j]))  # Log10 for power
        if peak_freq[j]:
            avg_peak_freq[j] = np.mean(peak_freq[j])
        if my_amp[j]:
            avg_my_amp[j] = np.mean(my_amp[j])
        if max_freq[j]:
            avg_max_freq[j] = np.mean(max_freq[j])

    # Convert lists for indices to numpy arrays (optional)
    final_start_ind = [np.array(lst) for lst in final_start_ind]
    final_end_ind = [np.array(lst) for lst in final_end_ind]
    lfo_start_ind = [np.array(lst) for lst in lfo_start_ind]
    lfo_end_ind = [np.array(lst) for lst in lfo_end_ind]
    noise_start_ind = [np.array(lst) for lst in noise_start_ind]
    noise_end_ind = [np.array(lst) for lst in noise_end_ind]





    # Calculate channel connectivity for each HFO detected in each channel
    nearby_delay = con_delay * samp_freq / 1000  # Time delay in points

    # Initialize the connectivity matrix
    connectivity = np.zeros((num_channels, num_channels))

    # Loop through each channel
    for i in range(start_channel - 1, end_channel):  # Adjust for zero-based indexing
        si = final_start_ind[i]  # Access the list element directly
        ei = final_end_ind[i]  # Access the list element directly
        k = 0

        # Loop through each HFO in channel i
        while k < len(si) and si[k] != 0:
            # Check for nearby HFOs in other channels
            for j in range(i + 1, end_channel):  # Look at channels after i
                if is_hfo_nearby(si[k], ei[k], nearby_delay, final_start_ind[j], final_end_ind[j]):  # MY fn CALL
                    connectivity[i, j] += 1
            k += 1

    # Initialize the connectivity factors
    con_fact = np.zeros(num_channels)  # Connectivity factor for each channel
    con_factN = np.zeros(num_channels)  # Normalized connectivity factor for each channel

    # Loop through each channel to compute the connectivity factors
    for i in range(start_channel - 1, end_channel):
        ct = 0  # Counter for connected channels
        # Sum up the connectivity values for channels before and after channel i
        for j in range(start_channel - 1, i):  # Channels before i
            con_fact[i] += connectivity[j, i]
            if connectivity[j, i] > 0:
                ct += 1
        for j in range(i + 1, end_channel):  # Channels after i
            con_fact[i] += connectivity[i, j]
            if connectivity[i, j] > 0:
                ct += 1

        # Normalize the connectivity factor
        if con_fact[i] > 0:
            con_factN[i] = con_fact[i] / (ct * num_min)  # Connectivity per number of connected channels per duration
        else:
            con_factN[i] = 0

    if gui_output:
        gui_output("done.\n")

    # Store results in a dictionary
    result = {
        "win_len2": win_len2,
        "stdHilbert": stdHilbert,
        "meanHilbert": meanHilbert,
        "RMS2": RMS2,
        "rejected_start_ind": rejected_start_ind,
        "rejected_end_ind": rejected_end_ind,
        "rejecLONG_start_ind": rejecLONG_start_ind,
        "rejecLONG_end_ind": rejecLONG_end_ind,
        "final_start_ind": final_start_ind,
        "final_end_ind": final_end_ind,
        "lfo_start_ind": lfo_start_ind,
        "lfo_end_ind": lfo_end_ind,
        "noise_start_ind": noise_start_ind,
        "noise_end_ind": noise_end_ind,
        "myEEG": myEEG,
        "channel_labels": channel_labels,
        "end_channel": end_channel,
        "con_fact": con_fact,
        "con_factN": con_factN,
        "datalen_resulted_sec": datalen_resulted_sec,
        "num_pts": num_pts,
        "samp_freq": samp_freq,
        "num_min": num_min,
        "counter": counter,
        "duration": duration,
        "hfo_power": hfo_power,
        "peak_freq": peak_freq,
        "my_amp": my_amp,
        "max_freq": max_freq,
        "density": density,
        "avg_duration": avg_duration,
        "avg_hfo_power": avg_hfo_power,
        "avg_peak_freq": avg_peak_freq,
        "avg_my_amp": avg_my_amp,
        "avg_max_freq": avg_max_freq,
        "rejected": rejected,
        "num_channels": num_channels,
        "start_channel": start_channel,
        "Schan": Schan,
        "Echan": Echan,
        "montage": montage,
        "success": True,
        "use_as_ref": use_as_ref,
        "analysis_start": analysis_start,
        "analysis_end": analysis_end,
        "segment_length": data_length_seconds
    }

    if gui_output:
        gui_output("HFO detection complete.")
    
    return result
