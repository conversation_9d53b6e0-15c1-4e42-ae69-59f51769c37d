"""
Time Window Extraction Module
Extracts analysis windows from EEG data preserving exact logic from original
"""

import numpy as np
from typing import Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class TimeWindowExtractor:
    """Handles time window extraction for HFO analysis"""

    @staticmethod
    def extract_analysis_window(
        eeg_data: np.ndarray,
        sampling_rate: float,
        analysis_start: float,
        analysis_end: float,
        gui_output: Optional[callable] = None
    ) -> Tuple[np.ndarray, int, int, int, int]:
        """
        Extract time window from EEG data for analysis.
        Preserves exact logic from original hfo_analysis.py lines 86-127.

        Args:
            eeg_data: Original EEG data (channels x samples)
            sampling_rate: Sampling frequency in Hz
            analysis_start: Start time in seconds
            analysis_end: End time in seconds (-1 for full data)
            gui_output: Optional callback for status messages

        Returns:
            Tuple of:
                - Extracted EEG segment
                - Start sample index
                - End sample index
                - Result start index (relative to extraction)
                - Result end index (relative to extraction)
        """
        orig_num_pts = eeg_data.shape[1]

        # === Convert analysis times to sample indices ===
        my_time_start = round(analysis_start * sampling_rate)
        my_time_end = round(analysis_end * sampling_rate) if analysis_end != -1 else orig_num_pts

        # === Validate parameters (preserving original checks) ===
        if analysis_start < 0:
            if gui_output:
                gui_output("ERROR: Impossible file analysis parameter... analysis_start (<0)... exiting.")
            raise ValueError("Analysis start time cannot be negative")

        if analysis_start == 0:
            my_time_start = 1  # MATLAB-style 1-based indexing adjustment

        if my_time_end > orig_num_pts:
            my_time_end = orig_num_pts

        # === Extract segment ===
        # Note: Original uses my_time_start-1 for 0-based Python indexing
        extracted_segment = eeg_data[:, my_time_start-1:my_time_end]

        # === Calculate result window (for output) ===
        # Result window is relative to analysis window
        result_start = 0  # Default: start from beginning of extracted segment
        result_end = -1   # Default: use entire extracted segment

        my_result_start = round(result_start * sampling_rate)
        my_result_end = round(result_end * sampling_rate) if result_end != -1 else my_time_end

        if result_start < 0:
            if gui_output:
                gui_output("ERROR: Impossible file analysis parameter... result_start (<0)... exiting.")
            raise ValueError("Result start time cannot be negative")

        if result_start == 0:
            my_result_start = my_time_start

        if my_result_end > my_time_end:
            my_result_end = my_time_end

        # === Provide feedback ===
        if gui_output:
            data_length_points = my_result_end - my_result_start
            data_length_seconds = data_length_points / sampling_rate
            time_interval_start = my_result_start / sampling_rate
            time_interval_end = my_result_end / sampling_rate
            data_length_minutes = data_length_seconds / 60

            gui_output(
                f'Length of data that will be analyzed: {data_length_points} points; '
                f'{data_length_seconds:.4f} sec '
                f'(between {time_interval_start:.4f} and {time_interval_end:.4f} sec time intervals); '
                f'{data_length_minutes:.4f} min'
            )

        return extracted_segment, my_time_start, my_time_end, my_result_start, my_result_end

    @staticmethod
    def calculate_data_length(
        start_index: int,
        end_index: int,
        sampling_rate: float
    ) -> Tuple[float, float]:
        """
        Calculate data length in seconds and minutes.

        Args:
            start_index: Start sample index
            end_index: End sample index
            sampling_rate: Sampling frequency in Hz

        Returns:
            Tuple of (length_in_seconds, length_in_minutes)
        """
        data_length_samples = end_index - start_index
        data_length_seconds = data_length_samples / sampling_rate
        data_length_minutes = data_length_seconds / 60

        return data_length_seconds, data_length_minutes