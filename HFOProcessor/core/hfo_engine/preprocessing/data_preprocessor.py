"""
Data Preprocessing Module
Coordinates all preprocessing steps preserving exact algorithm
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple, List
import logging

from .time_window_extractor import TimeWindowExtractor
from ..blank_processor import BlankProcessor
from ..montage_processor import MontageProcessor
from ..signal_filtering import SignalFilter

logger = logging.getLogger(__name__)


class DataPreprocessor:
    """Coordinates all data preprocessing steps for HFO analysis"""

    def __init__(self, sampling_rate: float):
        """
        Initialize preprocessor with component modules.

        Args:
            sampling_rate: Sampling frequency in Hz
        """
        self.sampling_rate = sampling_rate
        self.time_extractor = TimeWindowExtractor()
        self.blank_processor = BlankProcessor(sampling_rate)
        self.montage_processor = MontageProcessor()
        self.signal_filter = SignalFilter(sampling_rate)

    def preprocess(
        self,
        eeg_data: Dict[str, Any],
        analysis_start: float,
        analysis_end: float,
        montage: str,
        user_ref: Optional[str],
        low_cutoff: float,
        high_cutoff: float,
        remove_discontinuities: bool = True,
        gui_output: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Execute complete preprocessing pipeline.

        Args:
            eeg_data: EEG data dictionary with 'data', 'nbchan', 'srate', 'chanlocs'
            analysis_start: Start time in seconds
            analysis_end: End time in seconds
            montage: Montage type ('Bipolar', 'Average', 'Referential')
            user_ref: Reference channel for referential montage
            low_cutoff: Low frequency cutoff for HFO filtering
            high_cutoff: High frequency cutoff for HFO filtering
            remove_discontinuities: Whether to remove blank periods
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing:
                - processed_signal: Fully processed signal ready for HFO detection
                - channel_labels: Channel labels after montage
                - blank_starts: Start indices of blank periods
                - blank_ends: End indices of blank periods
                - time_info: Time window information
                - num_channels: Number of channels after processing
                - num_samples: Number of samples after processing
        """
        # === Step 1: Extract time window ===
        if gui_output:
            gui_output(f"\nAnalyzing file...")
            gui_output(f"Sampling rate: {self.sampling_rate}")
            gui_output(f"Total number of channels: {eeg_data['nbchan']}")
            gui_output(f"labels: {eeg_data['chanlocs']}")

        extracted_data, time_start, time_end, result_start, result_end = \
            self.time_extractor.extract_analysis_window(
                eeg_data['data'],
                self.sampling_rate,
                analysis_start,
                analysis_end,
                gui_output
            )

        # === Step 2: Find blank sections ===
        blank_starts, blank_ends, artifact_channels = \
            self.blank_processor.find_blanks(extracted_data, eeg_data['chanlocs'])

        # Store original blank positions
        blank_starts_original = np.array(blank_starts)
        blank_ends_original = np.array(blank_ends)

        # === Step 3: Apply montage ===
        if gui_output:
            gui_output(f'\nApplying {montage} montage...')

        montaged_signal, channel_labels = self.montage_processor.apply_montage(
            extracted_data,
            eeg_data['chanlocs'],
            montage,
            user_ref,
            gui_output
        )

        # Multiply by -1 (EEG convention: positive needs to be displayed as negative)
        montaged_signal = -1 * montaged_signal

        num_channels, num_samples_before = montaged_signal.shape

        if gui_output:
            gui_output(f'Number of channels that will be analyzed: {num_channels}')

        # === Step 4: Apply filters ===
        # Notch filter
        filtered_signal = self.signal_filter.apply_notch_filter(
            montaged_signal,
            gui_output=gui_output
        )

        # Bandpass filter for HFO detection
        filtered_signal = self.signal_filter.apply_bandpass_filter(
            filtered_signal,
            low_cutoff,
            high_cutoff,
            gui_output=gui_output
        )

        # === Step 5: Remove discontinuities if requested ===
        blank_starts_new = []
        if remove_discontinuities and len(blank_starts) > 0:
            # Mark and remove blank sections
            filtered_signal, blank_starts_new = self._remove_discontinuities(
                filtered_signal,
                blank_starts,
                blank_ends,
                gui_output
            )

        num_samples_after = filtered_signal.shape[1]

        # === Calculate time information ===
        data_length_seconds, data_length_minutes = \
            self.time_extractor.calculate_data_length(
                result_start,
                result_end,
                self.sampling_rate
            )

        return {
            'processed_signal': filtered_signal,
            'channel_labels': channel_labels,
            'blank_starts': np.array(blank_starts_new) if blank_starts_new else blank_starts_original,
            'blank_ends': blank_ends_original,
            'artifact_channels': artifact_channels,
            'time_info': {
                'time_start': time_start,
                'time_end': time_end,
                'result_start': result_start,
                'result_end': result_end,
                'duration_seconds': data_length_seconds,
                'duration_minutes': data_length_minutes
            },
            'num_channels': num_channels,
            'num_samples': num_samples_after,
            'montage_used': montage,
            'reference_used': user_ref if montage == 'Referential' else None
        }

    def _remove_discontinuities(
        self,
        signal: np.ndarray,
        blank_starts: List[int],
        blank_ends: List[int],
        gui_output: Optional[callable] = None
    ) -> Tuple[np.ndarray, List[int]]:
        """
        Remove blank/discontinuous sections from signal.
        Preserves exact logic from original implementation.

        Args:
            signal: Input signal
            blank_starts: Start indices of blanks
            blank_ends: End indices of blanks
            gui_output: Optional callback

        Returns:
            Tuple of (processed_signal, adjusted_blank_starts)
        """
        if gui_output:
            gui_output("\nRemoving blank/discontinuous data...")

        BLANK_MARKER = 112233  # Magic number from original

        # Expand blank regions and mark them
        blank_starts_new = []
        cum_length = 0

        for i in range(len(blank_starts)):
            tmplen = blank_ends[i] - blank_starts[i] + 1

            # Adjust boundaries
            start_adjusted = max(1, blank_starts[i] - tmplen)
            end_adjusted = min(signal.shape[1], blank_ends[i] + tmplen)

            # Mark blank data
            signal[:, start_adjusted:end_adjusted] = BLANK_MARKER

            # Track new positions after removal
            blank_starts_new.append(start_adjusted - cum_length)
            cum_length += (end_adjusted - start_adjusted)

        # Remove marked sections
        processed_signal = []
        for ch_idx in range(signal.shape[0]):
            ch_data = signal[ch_idx, :]
            ch_data_clean = ch_data[ch_data != BLANK_MARKER]
            processed_signal.append(ch_data_clean)

        processed_signal = np.array(processed_signal)

        if gui_output:
            gui_output("done.\n")

        return processed_signal, blank_starts_new