"""
Peak Validation Module
Validates HFO segments based on peak criteria preserving exact algorithm
"""

import numpy as np
from scipy.signal import find_peaks
from typing import Tu<PERSON>, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class PeakValidator:
    """Validates HFO segments based on peak criteria"""

    @staticmethod
    def validate_by_peaks(
        signal: np.ndarray,
        start_indices: np.ndarray,
        end_indices: np.ndarray,
        mean_signal_rect: np.ndarray,
        std_signal_rect: np.ndarray,
        baseline_threshold_factor: float,
        min_peaks_total: int,
        min_peaks_above_baseline: int,
        min_peaks_long_segment: int = 4,
        start_channel: int = 1,
        end_channel: Optional[int] = None
    ) -> Dict[str, np.ndarray]:
        """
        Validate HFOs based on peak criteria.
        Preserves exact logic from original hfo_analysis.py lines 551-650.

        Args:
            signal: Filtered EEG signal (channels x samples)
            start_indices: Start indices of HFO candidates
            end_indices: End indices of HFO candidates
            mean_signal_rect: Mean of rectified signal per channel
            std_signal_rect: StdDev of rectified signal per channel
            baseline_threshold_factor: Threshold multiplier (thresh2 in original)
            min_peaks_total: Minimum total peaks required (num_peaks in original)
            min_peaks_above_baseline: Min peaks above baseline (num_peaks2 in original)
            min_peaks_long_segment: Min peaks for long segment (num_peaks3 in original)
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)

        Returns:
            Dictionary containing:
                - validated_start: Start indices that pass validation
                - validated_end: End indices that pass validation
                - rejected_start: Rejected due to insufficient peaks
                - rejected_end: Rejected end indices
                - rejected_long_start: Rejected but has enough peaks for marking
                - rejected_long_end: Rejected long segment end indices
        """
        num_channels, num_detections = start_indices.shape

        if end_channel is None:
            end_channel = num_channels

        # === Initialize arrays for different categories ===
        rejected_start = np.zeros((num_channels, num_detections))
        rejected_end = np.zeros((num_channels, num_detections))
        rejected_long_start = np.zeros((num_channels, num_detections))
        rejected_long_end = np.zeros((num_channels, num_detections))

        # === Process each channel ===
        for j in range(start_channel - 1, end_channel):
            # Convert to list for easier manipulation
            si = list(start_indices[j, :])
            ei = list(end_indices[j, :])

            i = 0
            rejected_count = 0
            rejected_long_count = 0

            while i < len(si) and si[i] != 0:
                # Extract HFO segment
                hfo_segment = signal[j, si[i]:ei[i]]

                # Rectify the signal
                hfo_rectified = np.abs(hfo_segment)

                # Find local maxima (peaks)
                peaks, _ = find_peaks(hfo_rectified)

                # Calculate threshold for this channel
                threshold = mean_signal_rect[j] + baseline_threshold_factor * std_signal_rect[j]

                # Find peaks above threshold
                if len(peaks) > 0:
                    peaks_above = np.where(hfo_rectified[peaks] > threshold)[0]
                else:
                    peaks_above = np.array([])

                # === Apply validation criteria ===
                if len(peaks) < min_peaks_total or len(peaks_above) < min_peaks_above_baseline:
                    # Fails primary criteria
                    if len(peaks) >= min_peaks_long_segment:
                        # Has enough peaks for long segment marking
                        rejected_long_start[j, rejected_long_count] = si[i]
                        rejected_long_end[j, rejected_long_count] = ei[i]
                        rejected_long_count += 1
                    else:
                        # Regular rejection
                        rejected_start[j, rejected_count] = si[i]
                        rejected_end[j, rejected_count] = ei[i]
                        rejected_count += 1

                    # Remove from validated list
                    del si[i]
                    del ei[i]
                else:
                    # Passes validation
                    i += 1

            # Pad lists back to original size
            pad_length = num_detections - len(si)
            if pad_length > 0:
                si.extend([0] * pad_length)
                ei.extend([0] * pad_length)

            # Update validated indices
            start_indices[j, :] = si[:num_detections]
            end_indices[j, :] = ei[:num_detections]

        return {
            'validated_start': start_indices,
            'validated_end': end_indices,
            'rejected_start': rejected_start,
            'rejected_end': rejected_end,
            'rejected_long_start': rejected_long_start,
            'rejected_long_end': rejected_long_end
        }

    @staticmethod
    def check_near_blanks(
        start_indices: np.ndarray,
        end_indices: np.ndarray,
        blank_starts: np.ndarray,
        blank_ends: np.ndarray,
        start_channel: int = 1,
        end_channel: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Remove HFOs that occur near blank periods.
        Preserves logic from original near_blank_period function.

        Args:
            start_indices: HFO start indices
            end_indices: HFO end indices
            blank_starts: Start indices of blank periods
            blank_ends: End indices of blank periods
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)

        Returns:
            Filtered (start_indices, end_indices) with blanks removed
        """
        if len(blank_starts) == 0:
            return start_indices, end_indices

        num_channels, num_detections = start_indices.shape

        if end_channel is None:
            end_channel = num_channels

        # Process each channel
        for j in range(start_channel - 1, end_channel):
            si = list(start_indices[j, :])
            ei = list(end_indices[j, :])

            i = 0
            while i < len(si) and si[i] != 0:
                # Check if HFO is near any blank
                is_near_blank = PeakValidator._is_near_blank(
                    si[i], ei[i], blank_starts, blank_ends
                )

                if is_near_blank:
                    # Remove HFO
                    del si[i]
                    del ei[i]
                else:
                    i += 1

            # Pad back to original size
            pad_length = num_detections - len(si)
            if pad_length > 0:
                si.extend([0] * pad_length)
                ei.extend([0] * pad_length)

            start_indices[j, :] = si[:num_detections]
            end_indices[j, :] = ei[:num_detections]

        return start_indices, end_indices

    @staticmethod
    def _is_near_blank(
        hfo_start: int,
        hfo_end: int,
        blank_starts: np.ndarray,
        blank_ends: np.ndarray
    ) -> bool:
        """
        Check if HFO overlaps with or is near a blank period.

        Args:
            hfo_start: HFO start index
            hfo_end: HFO end index
            blank_starts: Array of blank start indices
            blank_ends: Array of blank end indices

        Returns:
            True if HFO is near a blank
        """
        for blank_start, blank_end in zip(blank_starts, blank_ends):
            # Check for any overlap
            if not (hfo_end < blank_start or hfo_start > blank_end):
                return True

        return False