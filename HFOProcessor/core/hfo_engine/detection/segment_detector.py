"""
HFO Segment Detection Module
Finds HFO segments based on energy thresholds preserving exact algorithm
"""

import numpy as np
from typing import Tuple, List, Optional, Dict
import logging

logger = logging.getLogger(__name__)


class SegmentDetector:
    """Detects HFO segments based on energy thresholds"""

    @staticmethod
    def find_hfo_segments(
        hilbert_envelope: np.ndarray,
        mean_hilbert: np.ndarray,
        std_hilbert: np.ndarray,
        threshold_factor: float,
        min_hfo_duration_ms: float,
        sampling_rate: float,
        start_channel: int = 1,
        end_channel: Optional[int] = None,
        threshold_scheme: int = 10,
        gui_output: Optional[callable] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Find HFO segments that exceed energy threshold.
        Preserves exact logic from original hfo_analysis.py lines 466-550.

        Args:
            hilbert_envelope: Hilbert transform of signal (channels x samples)
            mean_hilbert: Mean of Hilbert per channel
            std_hilbert: StdDev of Hilbert per channel
            threshold_factor: Threshold multiplier (thresh in original)
            min_hfo_duration_ms: Minimum HFO duration in milliseconds
            sampling_rate: Sampling frequency in Hz
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based, None for all)
            threshold_scheme: Thresholding scheme (only 10 supported)
            gui_output: Optional callback for status messages

        Returns:
            Tuple of (start_indices, end_indices) arrays
        """
        num_channels = hilbert_envelope.shape[0]
        num_samples = hilbert_envelope.shape[1]

        if end_channel is None:
            end_channel = num_channels

        if threshold_scheme != 10:
            raise ValueError('ERROR: only thresh_scheme 10 is available')

        if gui_output:
            gui_output('Finding HFOs... ')

        # === Convert minimum duration to samples ===
        min_duration_samples = min_hfo_duration_ms * sampling_rate / 1000

        # === Initialize lists for each channel ===
        all_starts = []
        all_ends = []

        # === Find segments for each channel ===
        for j in range(start_channel - 1, end_channel):
            # Calculate threshold for this channel
            threshold = mean_hilbert[j] + threshold_factor * std_hilbert[j]

            channel_starts = []
            channel_ends = []

            # State variables for segment detection
            flag = 0  # 0 = below threshold, 1 = above threshold
            ct = 0    # Counter for consecutive points above threshold

            # Scan through signal
            for i in range(num_samples):
                if hilbert_envelope[j, i] > threshold:
                    # Signal above threshold
                    flag = 1
                    ct += 1
                else:
                    # Signal below threshold
                    if flag == 1:
                        # End of above-threshold segment
                        if ct >= min_duration_samples:
                            # Segment is long enough - save it
                            channel_starts.append(i - ct)
                            channel_ends.append(i - 1)
                        flag = 0
                        ct = 0

            # Check if last segment extends to end of signal
            if flag == 1 and ct >= min_duration_samples:
                channel_starts.append(num_samples - ct)
                channel_ends.append(num_samples - 1)

            all_starts.append(channel_starts)
            all_ends.append(channel_ends)

        # === Convert to numpy arrays with padding ===
        max_detections = max(len(starts) for starts in all_starts) if all_starts else 0

        if max_detections == 0:
            # No HFOs detected
            start_indices = np.zeros((num_channels, 1), dtype=int)
            end_indices = np.zeros((num_channels, 1), dtype=int)
        else:
            # Pad arrays with zeros to make rectangular
            start_indices = np.zeros((num_channels, max_detections), dtype=int)
            end_indices = np.zeros((num_channels, max_detections), dtype=int)

            for ch in range(num_channels):
                if ch < len(all_starts):
                    num_detections = len(all_starts[ch])
                    if num_detections > 0:
                        start_indices[ch, :num_detections] = all_starts[ch]
                        end_indices[ch, :num_detections] = all_ends[ch]

        if gui_output:
            gui_output('...done.\n')

        return start_indices, end_indices

    @staticmethod
    def merge_nearby_segments(
        start_indices: np.ndarray,
        end_indices: np.ndarray,
        min_separation_ms: float,
        sampling_rate: float,
        start_channel: int = 1,
        end_channel: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Merge HFO segments that are close together.
        Preserves exact logic from original hfo_analysis.py.

        Args:
            start_indices: Start indices array (channels x detections)
            end_indices: End indices array (channels x detections)
            min_separation_ms: Minimum separation between HFOs in ms
            sampling_rate: Sampling frequency in Hz
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)

        Returns:
            Tuple of merged (start_indices, end_indices)
        """
        num_channels, num_detections = start_indices.shape

        if end_channel is None:
            end_channel = num_channels

        # Convert separation to samples
        min_separation_samples = min_separation_ms * sampling_rate / 1000

        # Process each channel
        for j in range(start_channel - 1, end_channel):
            # Convert to list for easier manipulation
            si = list(start_indices[j, :])
            ei = list(end_indices[j, :])

            i = 0
            while i < len(si) - 1:
                if si[i + 1] != 0:  # Valid detection
                    # Check if next segment is within minimum separation
                    if si[i + 1] < ei[i] + min_separation_samples:
                        # Merge segments
                        ei[i] = ei[i + 1]
                        del si[i + 1]
                        del ei[i + 1]
                    else:
                        i += 1
                else:
                    break  # No more valid detections

            # Pad back to original size with zeros
            pad_length = num_detections - len(si)
            if pad_length > 0:
                si.extend([0] * pad_length)
                ei.extend([0] * pad_length)

            # Update arrays
            start_indices[j, :] = si[:num_detections]
            end_indices[j, :] = ei[:num_detections]

        return start_indices, end_indices

    @staticmethod
    def count_hfos(
        start_indices: np.ndarray,
        start_channel: int = 1,
        end_channel: Optional[int] = None
    ) -> Tuple[int, Dict[int, int]]:
        """
        Count total HFOs and per-channel counts.

        Args:
            start_indices: Start indices array (channels x detections)
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)

        Returns:
            Tuple of (total_count, per_channel_counts)
        """
        num_channels = start_indices.shape[0]

        if end_channel is None:
            end_channel = num_channels

        total_count = 0
        per_channel_counts = {}

        for j in range(start_channel - 1, end_channel):
            # Count non-zero start indices
            channel_count = np.sum(start_indices[j, :] > 0)
            per_channel_counts[j + 1] = channel_count  # 1-based channel number
            total_count += channel_count

        return total_count, per_channel_counts