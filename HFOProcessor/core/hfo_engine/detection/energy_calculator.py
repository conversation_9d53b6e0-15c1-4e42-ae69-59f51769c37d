"""
Energy Calculation Module
Calculates energy statistics for HFO detection preserving exact algorithm
"""

import numpy as np
from scipy.signal import hilbert
from typing import Dict, <PERSON><PERSON>, Optional
import logging

logger = logging.getLogger(__name__)


class EnergyCalculator:
    """Calculates energy statistics for HFO detection"""

    @staticmethod
    def calculate_energy_statistics(
        signal: np.ndarray,
        sampling_rate: float,
        window_length_ms: float = 5.0,
        start_channel: int = 1,
        end_channel: Optional[int] = None,
        gui_output: Optional[callable] = None
    ) -> Dict[str, np.ndarray]:
        """
        Calculate energy statistics of the signal.
        Preserves exact logic from original hfo_analysis.py lines 406-465.

        Args:
            signal: Filtered EEG signal (channels x samples)
            sampling_rate: Sampling frequency in Hz
            window_length_ms: Window length in milliseconds for RMS calculation
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based, None for all)
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing:
                - mean_signal_rect: Mean of rectified signal per channel
                - std_signal_rect: StdDev of rectified signal per channel
                - hilbert_envelope: Hilbert transform of signal (RMS2 in original)
                - mean_hilbert: Mean of Hilbert transform per channel
                - std_hilbert: StdDev of Hilbert transform per channel
                - window_samples: Window length in samples
        """
        num_channels, num_samples = signal.shape

        if end_channel is None:
            end_channel = num_channels

        # === Convert window length to samples ===
        window_samples = round(window_length_ms * sampling_rate / 1000)

        # === Initialize arrays ===
        mean_signal_rect = np.zeros(num_channels)
        std_signal_rect = np.zeros(num_channels)
        hilbert_envelope = np.zeros((num_channels, num_samples - (window_samples - 1)))
        mean_hilbert = np.zeros(num_channels)
        std_hilbert = np.zeros(num_channels)

        if gui_output:
            gui_output('\nCalculating energy statistics of the signal...')
            gui_output('Rectifying signal...')

        # === Calculate rectified signal statistics ===
        # Note: Original uses 0-based indexing after subtracting 1 from channel numbers
        for j in range(start_channel - 1, end_channel):
            # Rectify signal (absolute value)
            rectified = np.abs(signal[j, :])

            # Calculate statistics
            std_signal_rect[j] = np.std(rectified)
            mean_signal_rect[j] = np.mean(rectified)

        if gui_output:
            gui_output('Rectification complete.')

        # === Perform Hilbert transform ===
        for j in range(start_channel - 1, end_channel):
            # Calculate Hilbert envelope (absolute value of analytic signal)
            # Note: Original truncates to account for window length
            analytic_signal = hilbert(signal[j, :num_samples - (window_samples - 1)])
            hilbert_envelope[j, :] = np.abs(analytic_signal)

            # Calculate Hilbert statistics
            std_hilbert[j] = np.std(hilbert_envelope[j, :])
            mean_hilbert[j] = np.mean(hilbert_envelope[j, :])

        if gui_output:
            gui_output('Energy statistics calculation done.')

        return {
            'mean_signal_rect': mean_signal_rect,
            'std_signal_rect': std_signal_rect,
            'hilbert_envelope': hilbert_envelope,  # RMS2 in original
            'mean_hilbert': mean_hilbert,
            'std_hilbert': std_hilbert,
            'window_samples': window_samples
        }

    @staticmethod
    def calculate_threshold(
        mean_energy: float,
        std_energy: float,
        threshold_factor: float,
        threshold_scheme: int = 10
    ) -> float:
        """
        Calculate detection threshold based on energy statistics.

        Args:
            mean_energy: Mean energy value
            std_energy: Standard deviation of energy
            threshold_factor: Threshold multiplier (thresh in original)
            threshold_scheme: Thresholding scheme (only 10 is supported)

        Returns:
            Calculated threshold value

        Raises:
            ValueError: If unsupported threshold scheme is specified
        """
        if threshold_scheme != 10:
            raise ValueError('ERROR: only thresh_scheme 10 is available')

        # Threshold scheme 10: meanHilbert + thresh * stdHilbert
        return mean_energy + threshold_factor * std_energy