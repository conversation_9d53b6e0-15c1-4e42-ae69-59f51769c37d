"""
Connectivity Calculation Module
Analyzes channel connectivity for HFO events preserving exact algorithm
"""

import numpy as np
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class ConnectivityCalculator:
    """Calculates connectivity between channels based on HFO timing"""

    @staticmethod
    def calculate_connectivity(
        final_start_ind: List[np.ndarray],
        final_end_ind: List[np.ndarray],
        connectivity_delay_ms: float,
        sampling_rate: float,
        start_channel: int = 1,
        end_channel: Optional[int] = None
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate channel connectivity based on HFO co-occurrence.
        Preserves exact logic from original hfo_analysis.py lines 751-830.

        Args:
            final_start_ind: List of start indices per channel
            final_end_ind: List of end indices per channel
            connectivity_delay_ms: Time delay in ms for connectivity (con_delay)
            sampling_rate: Sampling frequency in Hz
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)

        Returns:
            Tuple of:
                - connectivity_matrix: Channel connectivity matrix
                - connectivity_factor: Connectivity factor per channel
                - connectivity_factor_normalized: Normalized connectivity factor
        """
        num_channels = len(final_start_ind)

        if end_channel is None:
            end_channel = num_channels

        # === Convert delay to samples ===
        nearby_delay = connectivity_delay_ms * sampling_rate / 1000

        # === Initialize connectivity matrix ===
        connectivity_matrix = np.zeros((num_channels, num_channels))

        # === Calculate connectivity between channels ===
        for i in range(start_channel - 1, end_channel):
            si = final_start_ind[i]
            ei = final_end_ind[i]

            # Check each HFO in channel i
            k = 0
            while k < len(si) and si[k] != 0:
                # Check for nearby HFOs in other channels
                for j in range(i + 1, end_channel):
                    if ConnectivityCalculator._is_hfo_nearby(
                        si[k], ei[k], nearby_delay,
                        final_start_ind[j], final_end_ind[j]
                    ):
                        connectivity_matrix[i, j] += 1
                k += 1

        # === Calculate connectivity factors ===
        connectivity_factor = np.zeros(num_channels)
        connectivity_factor_normalized = np.zeros(num_channels)

        for i in range(start_channel - 1, end_channel):
            connected_channels = 0

            # Sum connectivity with channels before i
            for j in range(start_channel - 1, i):
                connectivity_factor[i] += connectivity_matrix[j, i]
                if connectivity_matrix[j, i] > 0:
                    connected_channels += 1

            # Sum connectivity with channels after i
            for j in range(i + 1, end_channel):
                connectivity_factor[i] += connectivity_matrix[i, j]
                if connectivity_matrix[i, j] > 0:
                    connected_channels += 1

            # Normalize connectivity factor
            # Note: Original uses num_min (duration in minutes) for normalization
            # This should be passed in, but for now we'll just return the raw factor
            if connectivity_factor[i] > 0 and connected_channels > 0:
                # Without duration normalization for now
                connectivity_factor_normalized[i] = connectivity_factor[i] / connected_channels
            else:
                connectivity_factor_normalized[i] = 0

        return connectivity_matrix, connectivity_factor, connectivity_factor_normalized

    @staticmethod
    def _is_hfo_nearby(
        start_index: int,
        end_index: int,
        nearby_delay: float,
        start_ind_vec: np.ndarray,
        end_ind_vec: np.ndarray
    ) -> bool:
        """
        Check if there is another HFO nearby.
        Direct port of isHFOnearby from original.

        Args:
            start_index: Start index of HFO being analyzed
            end_index: End index of HFO being analyzed
            nearby_delay: Allowed delay between HFOs (in samples)
            start_ind_vec: Vector of start indices in another channel
            end_ind_vec: Vector of end indices in another channel

        Returns:
            True if there is another HFO nearby
        """
        k = 0
        while k < len(start_ind_vec) and start_ind_vec[k] != 0:
            # Check for various types of overlap or proximity
            if start_ind_vec[k] < start_index and end_ind_vec[k] > start_index:
                return True
            elif start_ind_vec[k] < end_index and end_ind_vec[k] > end_index:
                return True
            elif start_ind_vec[k] > start_index and end_ind_vec[k] < end_index:
                return True
            elif start_ind_vec[k] > end_index and start_ind_vec[k] <= end_index + nearby_delay:
                return True
            elif end_ind_vec[k] >= start_index - nearby_delay and end_ind_vec[k] < start_index:
                return True
            elif start_ind_vec[k] > end_index + nearby_delay:
                return False  # No nearby HFOs, exit early

            k += 1

        return False  # No nearby HFOs found

    @staticmethod
    def normalize_with_duration(
        connectivity_factor: np.ndarray,
        connected_channels: np.ndarray,
        duration_minutes: float
    ) -> np.ndarray:
        """
        Normalize connectivity factor with duration.

        Args:
            connectivity_factor: Raw connectivity factors
            connected_channels: Number of connected channels per channel
            duration_minutes: Duration of analysis in minutes

        Returns:
            Normalized connectivity factors
        """
        normalized = np.zeros_like(connectivity_factor)

        for i in range(len(connectivity_factor)):
            if connectivity_factor[i] > 0 and connected_channels[i] > 0:
                # Connectivity per connected channel per minute
                normalized[i] = connectivity_factor[i] / (connected_channels[i] * duration_minutes)

        return normalized