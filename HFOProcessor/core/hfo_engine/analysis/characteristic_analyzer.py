"""
HFO Characteristic Analysis Module
Calculates HFO characteristics preserving exact algorithm from original
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging

from ..hfo_characteristics import calculate_HFO_characteristics

logger = logging.getLogger(__name__)


class CharacteristicAnalyzer:
    """Analyzes characteristics of detected HFOs"""

    @staticmethod
    def analyze_hfo_characteristics(
        signal: np.ndarray,
        start_indices: np.ndarray,
        end_indices: np.ndarray,
        sampling_rate: float,
        result_start: int,
        result_end: int,
        noise_freq_removal: int = 0,
        noise_freq_vector: Optional[List[float]] = None,
        hfo_freq: float = 70,
        max_freq: Optional[float] = None,
        power_threshold: float = 1,
        start_channel: int = 1,
        end_channel: Optional[int] = None,
        gui_output: Optional[callable] = None
    ) -> Dict[str, Any]:
        """
        Calculate characteristics for all detected HFOs.
        Preserves exact logic from original hfo_analysis.py lines 651-750.

        Args:
            signal: Filtered EEG signal (channels x samples)
            start_indices: Start indices of validated HFOs
            end_indices: End indices of validated HFOs
            sampling_rate: Sampling frequency in Hz
            result_start: Start index for results window
            result_end: End index for results window
            noise_freq_removal: Flag for noise frequency removal
            noise_freq_vector: Frequencies to consider as noise
            hfo_freq: Threshold frequency for HFO
            max_freq: Maximum frequency (default: sampling_rate/3)
            power_threshold: Power threshold for HFO detection
            start_channel: First channel to analyze (1-based)
            end_channel: Last channel to analyze (1-based)
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing:
                - final_start_ind: Start indices of true HFOs
                - final_end_ind: End indices of true HFOs
                - lfo_start_ind: Start indices of low-frequency oscillations
                - lfo_end_ind: End indices of low-frequency oscillations
                - noise_start_ind: Start indices of noise-frequency oscillations
                - noise_end_ind: End indices of noise-frequency oscillations
                - counter: Number of HFOs per channel
                - duration: HFO durations per channel
                - hfo_power: Power in HFO frequency range
                - peak_freq: Peak frequency from FFT
                - my_amp: Mean amplitude of rectified signal
                - max_freq_list: Maximum frequency
                - density: Sum of durations per channel
                - averages: Dictionary of average values
                - rejected_counts: Rejection statistics
        """
        num_channels = signal.shape[0]
        num_samples = signal.shape[1]

        if end_channel is None:
            end_channel = num_channels

        if max_freq is None:
            max_freq = round(sampling_rate / 3)

        if noise_freq_vector is None:
            noise_freq_vector = []

        if gui_output:
            gui_output('\nCalculating HFO characteristics...')

        # === Initialize storage lists ===
        counter = np.zeros(num_channels)
        duration = [[] for _ in range(num_channels)]
        hfo_power = [[] for _ in range(num_channels)]
        peak_freq = [[] for _ in range(num_channels)]
        my_amp = [[] for _ in range(num_channels)]
        max_freq_list = [[] for _ in range(num_channels)]
        density = np.zeros(num_channels)

        # Rejection counts
        rejected_lfo = 0
        rejected_noise = 0

        # Result indices (lists for dynamic sizing)
        final_start_ind = [[] for _ in range(num_channels)]
        final_end_ind = [[] for _ in range(num_channels)]
        lfo_start_ind = [[] for _ in range(num_channels)]
        lfo_end_ind = [[] for _ in range(num_channels)]
        noise_start_ind = [[] for _ in range(num_channels)]
        noise_end_ind = [[] for _ in range(num_channels)]

        # === Adjust result window (from original logic) ===
        # Result window is relative to extraction
        my_result_end = result_end - result_start
        my_result_start = 0

        # === Process each channel ===
        for j in range(start_channel - 1, end_channel):
            i = 0

            # Process each detected HFO
            while i < start_indices.shape[1] and start_indices[j, i] != 0:

                # Check if HFO is within result window
                if my_result_start <= start_indices[j, i] <= my_result_end:

                    # Extract HFO segment
                    hfo_segment = signal[j, start_indices[j, i]:end_indices[j, i]]

                    # Calculate characteristics using original function
                    flag, td, tpp, tphfo, tpf, ttp, tmf = calculate_HFO_characteristics(
                        hfo_segment,
                        sampling_rate,
                        noise_freq_removal,
                        noise_freq_vector,
                        hfo_freq,
                        max_freq,
                        power_threshold
                    )

                    # === Categorize based on flag ===
                    if flag == 1:  # True HFO
                        counter[j] += 1
                        duration[j].append(td)
                        hfo_power[j].append(tphfo)
                        peak_freq[j].append(tpf)
                        my_amp[j].append(ttp)
                        max_freq_list[j].append(tmf)
                        density[j] += td
                        final_start_ind[j].append(start_indices[j, i])
                        final_end_ind[j].append(end_indices[j, i])

                    elif flag == 0:  # Low frequency oscillation
                        rejected_lfo += 1
                        lfo_start_ind[j].append(start_indices[j, i])
                        lfo_end_ind[j].append(end_indices[j, i])

                    else:  # flag == 2, Noise frequency oscillation
                        rejected_noise += 1
                        noise_start_ind[j].append(start_indices[j, i])
                        noise_end_ind[j].append(end_indices[j, i])

                i += 1

        # === Calculate averages ===
        avg_duration = np.zeros(num_channels)
        avg_hfo_power = np.zeros(num_channels)
        avg_peak_freq = np.zeros(num_channels)
        avg_my_amp = np.zeros(num_channels)
        avg_max_freq = np.zeros(num_channels)

        for j in range(num_channels):
            if duration[j]:
                avg_duration[j] = np.mean(duration[j])
            if hfo_power[j]:
                avg_hfo_power[j] = np.mean(np.log10(hfo_power[j]))  # Log10 for power
            if peak_freq[j]:
                avg_peak_freq[j] = np.mean(peak_freq[j])
            if my_amp[j]:
                avg_my_amp[j] = np.mean(my_amp[j])
            if max_freq_list[j]:
                avg_max_freq[j] = np.mean(max_freq_list[j])

        # === Convert lists to numpy arrays ===
        final_start_ind = [np.array(lst) for lst in final_start_ind]
        final_end_ind = [np.array(lst) for lst in final_end_ind]
        lfo_start_ind = [np.array(lst) for lst in lfo_start_ind]
        lfo_end_ind = [np.array(lst) for lst in lfo_end_ind]
        noise_start_ind = [np.array(lst) for lst in noise_start_ind]
        noise_end_ind = [np.array(lst) for lst in noise_end_ind]

        if gui_output:
            gui_output('done.\n')

        return {
            'final_start_ind': final_start_ind,
            'final_end_ind': final_end_ind,
            'lfo_start_ind': lfo_start_ind,
            'lfo_end_ind': lfo_end_ind,
            'noise_start_ind': noise_start_ind,
            'noise_end_ind': noise_end_ind,
            'counter': counter,
            'duration': duration,
            'hfo_power': hfo_power,
            'peak_freq': peak_freq,
            'my_amp': my_amp,
            'max_freq': max_freq_list,
            'density': density,
            'averages': {
                'avg_duration': avg_duration,
                'avg_hfo_power': avg_hfo_power,
                'avg_peak_freq': avg_peak_freq,
                'avg_my_amp': avg_my_amp,
                'avg_max_freq': avg_max_freq
            },
            'rejected_counts': {
                'low_frequency': rejected_lfo,
                'noise_frequency': rejected_noise
            }
        }