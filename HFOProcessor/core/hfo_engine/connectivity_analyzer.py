"""
Connectivity analysis module for HFO detection.
Analyzes temporal and spatial connectivity between detected HFOs.
"""

import numpy as np
from typing import List, Tuple
from core.hfo_engine.hfo_proximity import is_hfo_nearby


class ConnectivityAnalyzer:
    """Analyzes connectivity between HFOs across channels"""

    def __init__(self, sampling_rate: float):
        """Initialize connectivity analyzer"""
        self.sampling_rate = sampling_rate

    def calculate_channel_connectivity(
        self,
        start_indices: np.ndarray,
        end_indices: np.ndarray,
        num_channels: int,
        con_delay: float
    ) -> np.ndarray:
        """
        Calculate connectivity matrix between channels

        Args:
            start_indices: Start indices array (channels x detections)
            end_indices: End indices array (channels x detections)
            num_channels: Number of channels
            con_delay: Connectivity delay in milliseconds

        Returns:
            Connectivity matrix (channels x channels)
        """
        nearby_delay = con_delay * self.sampling_rate / 1000
        connectivity = np.zeros((num_channels, num_channels))

        for i in range(num_channels):
            si = start_indices[i]
            ei = end_indices[i]

            for k in range(len(si)):
                if si[k] == 0:
                    break

                for j in range(i + 1, num_channels):
                    if is_hfo_nearby(si[k], ei[k], nearby_delay,
                                   start_indices[j], end_indices[j]):
                        connectivity[i, j] += 1

        return connectivity

    def calculate_connectivity_factor(
        self,
        connectivity: np.ndarray,
        num_channels: int
    ) -> np.ndarray:
        """
        Calculate connectivity factor for each channel

        Args:
            connectivity: Connectivity matrix
            num_channels: Number of channels

        Returns:
            Connectivity factor array
        """
        con_fact = np.zeros(num_channels)

        for i in range(num_channels):
            # Sum connectivity from channels before and after
            for j in range(i):
                con_fact[i] += connectivity[j, i]
            for j in range(i + 1, num_channels):
                con_fact[i] += connectivity[i, j]

        return con_fact

    @staticmethod
    def calculate_connectivity(
        final_start_ind: List[List],
        final_end_ind: List[List],
        con_delay: float,
        samp_freq: float,
        num_channels: int,
        start_channel: int,
        end_channel: int,
        num_min: float
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Calculate channel connectivity for HFOs.
        
        This function was extracted from the main algorithm to improve organization
        while preserving the exact connectivity calculation logic.
        
        Args:
            final_start_ind: Start indices of HFOs for each channel
            final_end_ind: End indices of HFOs for each channel
            con_delay: Time delay in milliseconds for connectivity
            samp_freq: Sampling frequency
            num_channels: Total number of channels
            start_channel: Starting channel (1-based)
            end_channel: Ending channel (1-based)
            num_min: Duration in minutes for normalization
            
        Returns:
            Tuple of (connectivity matrix, connectivity factor, normalized connectivity factor)
        """
        # Calculate channel connectivity for each HFO detected in each channel
        nearby_delay = con_delay * samp_freq / 1000  # Time delay in points
        
        # Initialize the connectivity matrix
        connectivity = np.zeros((num_channels, num_channels))
        
        # Loop through each channel
        for i in range(start_channel - 1, end_channel):  # Adjust for zero-based indexing
            si = final_start_ind[i]  # Access the list element directly
            ei = final_end_ind[i]  # Access the list element directly
            k = 0
            
            # Loop through each HFO in channel i
            while k < len(si) and si[k] != 0:
                # Check for nearby HFOs in other channels
                for j in range(i + 1, end_channel):  # Look at channels after i
                    if is_hfo_nearby(si[k], ei[k], nearby_delay, final_start_ind[j], final_end_ind[j]):
                        connectivity[i, j] += 1
                k += 1
        
        # Initialize the connectivity factors
        con_fact = np.zeros(num_channels)  # Connectivity factor for each channel
        con_factN = np.zeros(num_channels)  # Normalized connectivity factor for each channel
        
        # Loop through each channel to compute the connectivity factors
        for i in range(start_channel - 1, end_channel):
            ct = 0  # Counter for connected channels
            # Sum up the connectivity values for channels before and after channel i
            for j in range(start_channel - 1, i):  # Channels before i
                con_fact[i] += connectivity[j, i]
                if connectivity[j, i] > 0:
                    ct += 1
            for j in range(i + 1, end_channel):  # Channels after i
                con_fact[i] += connectivity[i, j]
                if connectivity[i, j] > 0:
                    ct += 1
            
            # Normalize the connectivity factor
            if con_fact[i] > 0:
                con_factN[i] = con_fact[i] / (ct * num_min)  # Connectivity per number of connected channels per duration
            else:
                con_factN[i] = 0
        
        return connectivity, con_fact, con_factN