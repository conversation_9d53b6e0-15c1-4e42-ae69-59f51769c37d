"""
HFO Analysis Modular Orchestrator
Main entry point that coordinates all HFO processing modules
"""

import numpy as np
from typing import Dict, Any, Optional, List
import logging
import os

from .preprocessing import DataPreprocessor
from .detection import EnergyCalculator, SegmentDetector, PeakValidator
from .analysis import CharacteristicA<PERSON>yzer, ConnectivityCalculator
from .blank_processor import BlankProcessor
from .montage_processor import MontageProcessor

logger = logging.getLogger(__name__)


def run_hfo_algorithm(
    EEG: Dict[str, Any],
    input_file_path: str,
    analysis_start: float = 0,
    analysis_end: float = -1,
    montage: str = 'Referential',
    user_ref: Optional[str] = None,
    con_delay: int = 10,
    threshold1: float = 5,
    threshold2: float = 3,
    num_peaks: int = 6,
    num_peaks2: int = 0,
    num_peaks3: int = 4,
    low_cutoff: float = 80,
    high_cutoff: float = 500,
    cfa: int = 0,
    cfa_low_cutoff: float = 40,
    cfa_high_cutoff: float = 80,
    remove_discontinuities: bool = True,
    start_channel: int = 1,
    end_channel: Optional[int] = None,
    threshold_option: int = 10,
    window_length_ms: float = 5,
    min_separation_ms: float = 10,
    noise_freq_removal: int = 0,
    noise_freq_vector: Optional[List[float]] = None,
    hfo_freq: float = 70,
    max_freq: Optional[float] = None,
    power_threshold: float = 1,
    output_dir: Optional[str] = None,
    save_results: bool = True,
    gui_output: Optional[callable] = None,
    user_id: str = "unknown",
    job_id: str = "unknown",
    reference_channels: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Main HFO analysis function using modular components.

    Args:
        EEG: Dictionary with 'data', 'srate', 'nbchan', 'chanlocs', etc.
        input_file_path: Path to input EDF file
        analysis_start: Start time in seconds
        analysis_end: End time in seconds (-1 for entire file)
        montage: Montage type ('Referential', 'Bipolar', 'Average')
        user_ref: Reference channel for referential montage
        con_delay: Connectivity delay in ms
        threshold1: Primary detection threshold (STD multiplier)
        threshold2: Secondary validation threshold
        num_peaks: Minimum number of peaks required
        num_peaks2: Minimum peaks above baseline
        num_peaks3: Minimum peaks for long segments
        low_cutoff: Low frequency cutoff for HFO filter
        high_cutoff: High frequency cutoff for HFO filter
        cfa: Complex Frequency Analysis flag
        cfa_low_cutoff: CFA low frequency cutoff
        cfa_high_cutoff: CFA high frequency cutoff
        remove_discontinuities: Whether to remove blank periods
        start_channel: First channel to analyze (1-based)
        end_channel: Last channel to analyze (1-based)
        threshold_option: Threshold scheme (only 10 supported)
        window_length_ms: Window length for energy calculation
        min_separation_ms: Minimum separation between HFOs
        noise_freq_removal: Flag for noise frequency removal
        noise_freq_vector: Frequencies to consider as noise
        hfo_freq: Threshold frequency for HFO
        max_freq: Maximum frequency
        power_threshold: Power threshold for HFO detection
        output_dir: Directory for output files
        save_results: Whether to save results to files
        gui_output: Optional callback for status messages
        user_id: User identifier
        job_id: Job identifier
        reference_channels: List of reference channels

    Returns:
        Dictionary containing all HFO analysis results
    """

    try:
        # Validate threshold option
        if threshold_option != 10:
            raise ValueError(f"Only threshold option 10 is supported, got {threshold_option}")

        # Extract sampling rate and prepare data
        sampling_rate = EEG['srate']
        num_channels_orig = EEG['nbchan']

        if end_channel is None:
            end_channel = num_channels_orig

        if max_freq is None:
            max_freq = round(sampling_rate / 3)

        if noise_freq_vector is None:
            noise_freq_vector = []

        # === Step 1: Preprocessing ===
        preprocessor = DataPreprocessor(sampling_rate)
        preprocessing_result = preprocessor.preprocess(
            EEG,
            analysis_start,
            analysis_end,
            montage,
            user_ref,
            low_cutoff,
            high_cutoff,
            remove_discontinuities,
            gui_output
        )

        processed_signal = preprocessing_result['processed_signal']
        channel_labels = preprocessing_result['channel_labels']
        blank_starts = preprocessing_result['blank_starts']
        blank_ends = preprocessing_result['blank_ends']
        time_info = preprocessing_result['time_info']
        num_channels = preprocessing_result['num_channels']
        num_samples = preprocessing_result['num_samples']

        # === Step 2: CFA Processing (if enabled) ===
        cfa_signal = None
        if cfa == 1:
            if gui_output:
                gui_output('\nApplying CFA filter...')
            from .signal_filtering import SignalFilter
            filter = SignalFilter(sampling_rate)
            cfa_signal = filter.apply_bandpass_filter(
                processed_signal.copy(),
                cfa_low_cutoff,
                cfa_high_cutoff,
                filter_order=4,
                gui_output=gui_output
            )

        # === Step 3: Energy Calculation ===
        if gui_output:
            gui_output('\nCalculating energy statistics...')

        energy_stats = EnergyCalculator.calculate_energy_statistics(
            processed_signal,
            sampling_rate,
            window_length_ms,
            start_channel,
            end_channel
        )

        hilbert_envelope = energy_stats['hilbert_envelope']
        mean_hilbert = energy_stats['mean_hilbert']
        std_hilbert = energy_stats['std_hilbert']
        signal_rectified = energy_stats['signal_rectified']
        mean_signal_rect = energy_stats['mean_signal_rect']
        std_signal_rect = energy_stats['std_signal_rect']

        # === Step 4: HFO Segment Detection ===
        if gui_output:
            gui_output('\nDetecting HFO segments...')

        detection_result = SegmentDetector.find_hfo_segments(
            hilbert_envelope,
            mean_hilbert,
            std_hilbert,
            threshold1,
            min_separation_ms,
            sampling_rate,
            start_channel,
            end_channel
        )

        start_indices = detection_result['start_indices']
        end_indices = detection_result['end_indices']
        rejected_short_start = detection_result['rejected_short_start']
        rejected_short_end = detection_result['rejected_short_end']

        # === Step 5: CFA Validation (if enabled) ===
        if cfa == 1 and cfa_signal is not None:
            if gui_output:
                gui_output('\nApplying CFA validation...')
            from .cfa_validator import CFAValidator
            start_indices, end_indices = CFAValidator.validate_with_cfa(
                start_indices,
                end_indices,
                cfa_signal,
                start_channel,
                end_channel
            )

        # === Step 6: Peak Validation ===
        if gui_output:
            gui_output('\nValidating HFOs by peak criteria...')

        validation_result = PeakValidator.validate_by_peaks(
            processed_signal,
            start_indices,
            end_indices,
            mean_signal_rect,
            std_signal_rect,
            threshold2,
            num_peaks,
            num_peaks2,
            num_peaks3,
            start_channel,
            end_channel
        )

        start_indices = validation_result['validated_start']
        end_indices = validation_result['validated_end']
        rejected_start = validation_result['rejected_start']
        rejected_end = validation_result['rejected_end']
        rejected_long_start = validation_result['rejected_long_start']
        rejected_long_end = validation_result['rejected_long_end']

        # === Step 7: Check Near Blanks ===
        if len(blank_starts) > 0:
            start_indices, end_indices = PeakValidator.check_near_blanks(
                start_indices,
                end_indices,
                blank_starts,
                blank_ends,
                start_channel,
                end_channel
            )

        # === Step 8: Calculate HFO Characteristics ===
        if gui_output:
            gui_output('\nAnalyzing HFO characteristics...')

        characteristics = CharacteristicAnalyzer.analyze_hfo_characteristics(
            processed_signal,
            start_indices,
            end_indices,
            sampling_rate,
            time_info['result_start'],
            time_info['result_end'],
            noise_freq_removal,
            noise_freq_vector,
            hfo_freq,
            max_freq,
            power_threshold,
            start_channel,
            end_channel,
            gui_output
        )

        final_start_ind = characteristics['final_start_ind']
        final_end_ind = characteristics['final_end_ind']
        lfo_start_ind = characteristics['lfo_start_ind']
        lfo_end_ind = characteristics['lfo_end_ind']
        noise_start_ind = characteristics['noise_start_ind']
        noise_end_ind = characteristics['noise_end_ind']
        counter = characteristics['counter']
        duration = characteristics['duration']
        hfo_power = characteristics['hfo_power']
        peak_freq = characteristics['peak_freq']
        my_amp = characteristics['my_amp']
        max_freq_list = characteristics['max_freq']
        density = characteristics['density']
        averages = characteristics['averages']
        rejected_counts = characteristics['rejected_counts']

        # === Step 9: Calculate Connectivity ===
        if gui_output:
            gui_output('\nCalculating channel connectivity...')

        connectivity_matrix, connectivity_factor, connectivity_factor_normalized = \
            ConnectivityCalculator.calculate_connectivity(
                final_start_ind,
                final_end_ind,
                con_delay,
                sampling_rate,
                start_channel,
                end_channel
            )

        # Normalize connectivity with duration
        duration_minutes = time_info['duration_minutes']
        if duration_minutes > 0:
            connected_channels = np.zeros(num_channels)
            for i in range(num_channels):
                for j in range(num_channels):
                    if i != j and (connectivity_matrix[i, j] > 0 or connectivity_matrix[j, i] > 0):
                        connected_channels[i] += 1

            connectivity_factor_normalized = ConnectivityCalculator.normalize_with_duration(
                connectivity_factor,
                connected_channels,
                duration_minutes
            )

        # === Step 10: Format Results ===
        if gui_output:
            gui_output('\nFormatting results...')

        results = {
            'sampling_rate': sampling_rate,
            'num_channels': num_channels,
            'channel_labels': channel_labels,
            'time_info': time_info,
            'montage': montage,
            'reference': user_ref if montage == 'Referential' else None,

            # Detection parameters
            'parameters': {
                'threshold1': threshold1,
                'threshold2': threshold2,
                'num_peaks': num_peaks,
                'num_peaks2': num_peaks2,
                'num_peaks3': num_peaks3,
                'low_cutoff': low_cutoff,
                'high_cutoff': high_cutoff,
                'cfa': cfa,
                'cfa_low_cutoff': cfa_low_cutoff if cfa == 1 else None,
                'cfa_high_cutoff': cfa_high_cutoff if cfa == 1 else None,
                'con_delay': con_delay,
                'window_length_ms': window_length_ms,
                'min_separation_ms': min_separation_ms,
                'noise_freq_removal': noise_freq_removal,
                'noise_freq_vector': noise_freq_vector,
                'hfo_freq': hfo_freq,
                'max_freq': max_freq,
                'power_threshold': power_threshold
            },

            # HFO results
            'hfo_detections': {
                'final_start_ind': final_start_ind,
                'final_end_ind': final_end_ind,
                'lfo_start_ind': lfo_start_ind,
                'lfo_end_ind': lfo_end_ind,
                'noise_start_ind': noise_start_ind,
                'noise_end_ind': noise_end_ind,
                'counter': counter.tolist(),
                'duration': duration,
                'hfo_power': hfo_power,
                'peak_freq': peak_freq,
                'amplitude': my_amp,
                'max_freq': max_freq_list,
                'density': density.tolist()
            },

            # Averages
            'averages': averages,

            # Connectivity
            'connectivity': {
                'matrix': connectivity_matrix.tolist(),
                'factor': connectivity_factor.tolist(),
                'factor_normalized': connectivity_factor_normalized.tolist()
            },

            # Rejection statistics
            'rejections': {
                'short_duration': {
                    'start': rejected_short_start.tolist() if isinstance(rejected_short_start, np.ndarray) else [],
                    'end': rejected_short_end.tolist() if isinstance(rejected_short_end, np.ndarray) else []
                },
                'insufficient_peaks': {
                    'start': rejected_start.tolist(),
                    'end': rejected_end.tolist()
                },
                'long_segments': {
                    'start': rejected_long_start.tolist(),
                    'end': rejected_long_end.tolist()
                },
                'low_frequency': rejected_counts['low_frequency'],
                'noise_frequency': rejected_counts['noise_frequency']
            },

            # Blank sections
            'blanks': {
                'starts': blank_starts.tolist() if isinstance(blank_starts, np.ndarray) else [],
                'ends': blank_ends.tolist() if isinstance(blank_ends, np.ndarray) else []
            },

            # Metadata
            'metadata': {
                'user_id': user_id,
                'job_id': job_id,
                'input_file': os.path.basename(input_file_path),
                'analysis_timestamp': None,  # Will be set by caller
                'reference_channels': reference_channels
            }
        }

        # === Step 11: Save Results (if requested) ===
        # Note: save_results is always False in production (set in pipeline.py)
        # This section is dead code that was using a non-existent ResultFormatter class
        # if save_results and output_dir:
        #     # This code would need a proper save implementation if ever enabled
        #     pass

        if gui_output:
            gui_output('\nHFO analysis completed successfully.')

        return results

    except Exception as e:
        logger.error(f"HFO analysis failed: {str(e)}")
        if gui_output:
            gui_output(f'\nError during HFO analysis: {str(e)}')
        raise