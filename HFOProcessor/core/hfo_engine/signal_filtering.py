"""
Signal Filtering Module
Handles all filtering operations for HFO detection
"""

import numpy as np
from scipy.signal import butter, filtfilt
import logging
from typing import Tuple, List, Optional, Dict

from core.constants import (
    DEFAULT_Q_FACTOR, NOTCH_BASE_FREQ, DEFAULT_LOW_CUTOFF,
    DEFAULT_HIGH_CUTOFF, CFA_LOW_FREQ, CFA_HIGH_FREQ
)

logger = logging.getLogger(__name__)


class SignalFilter:
    """Handles all signal filtering operations"""

    def __init__(self, sampling_rate: float):
        """
        Initialize signal filter

        Args:
            sampling_rate: Sampling frequency in Hz
        """
        self.sampling_rate = sampling_rate
        self.nyquist_freq = sampling_rate / 2

        # Validate sampling rate
        if sampling_rate > 2048:
            raise ValueError(
                f"Sampling rate {sampling_rate}Hz is too high (max: 2048Hz)")

    def apply_all_filters(self, signal: np.ndarray,
                          low_cutoff: float = DEFAULT_LOW_CUTOFF,
                          high_cutoff: float = DEFAULT_HIGH_CUTOFF,
                          apply_notch: bool = True,
                          apply_bandpass: bool = True,
                          apply_cfa: bool = True,
                          gui_output: Optional[callable] = None) -> Dict[str, np.ndarray]:
        """
        Apply all required filters to the signal

        Args:
            signal: Input EEG signal (channels x samples)
            low_cutoff: Low frequency cutoff for HFO detection
            high_cutoff: High frequency cutoff for HFO detection
            apply_notch: Whether to apply notch filtering
            apply_bandpass: Whether to apply bandpass filtering
            apply_cfa: Whether to create CFA filtered signal
            gui_output: Optional callback for status messages

        Returns:
            Dictionary containing filtered signals
        """
        num_channels, num_samples = signal.shape
        filtered_signal = signal.copy()
        results = {}

        # Apply notch filter
        if apply_notch:
            filtered_signal = self.apply_notch_filter(
                filtered_signal, gui_output=gui_output
            )
            results['notch_filtered'] = filtered_signal.copy()

        # Apply bandpass filter for HFO detection
        if apply_bandpass:
            hfo_signal = self.apply_bandpass_filter(
                filtered_signal, low_cutoff, high_cutoff, gui_output=gui_output
            )
            results['hfo_filtered'] = hfo_signal
        else:
            results['hfo_filtered'] = filtered_signal

        # Create CFA filtered signal
        if apply_cfa:
            cfa_signal = self.create_cfa_signal(
                signal, gui_output=gui_output
            )
            results['cfa_filtered'] = cfa_signal

        return results

    def apply_notch_filter(self, signal: np.ndarray,
                           q_factor: float = DEFAULT_Q_FACTOR,
                           gui_output: Optional[callable] = None) -> np.ndarray:
        """
        Apply notch filter at 60Hz and its harmonics

        Args:
            signal: Input signal (channels x samples)
            q_factor: Quality factor for notch filter
            gui_output: Optional callback for status messages

        Returns:
            Notch filtered signal
        """
        if gui_output:
            gui_output("\nPerforming notch filtering...")

        # Calculate harmonics to remove
        notch_frequencies = self._calculate_notch_frequencies()

        filtered_signal = signal.copy()

        for notch_freq in notch_frequencies:
            if gui_output:
                gui_output(f"Removing {notch_freq}Hz ")

            # Create notch filter
            b, a = self._create_notch_filter(notch_freq, q_factor)

            # Apply to all channels
            for ch in range(signal.shape[0]):
                # Check if channel has sufficient samples for filtering
                if filtered_signal[ch, :].size >= 10:
                    filtered_signal[ch, :] = filtfilt(b, a, filtered_signal[ch, :])
                # Skip filtering if insufficient samples

        if gui_output:
            gui_output("Notch filtering complete.")

        return filtered_signal

    def apply_bandpass_filter(self, signal: np.ndarray,
                              low_cutoff: float,
                              high_cutoff: float,
                              filter_order: int = 6,
                              gui_output: Optional[callable] = None) -> np.ndarray:
        """
        Apply bandpass filter for HFO detection

        Args:
            signal: Input signal (channels x samples)
            low_cutoff: Low frequency cutoff
            high_cutoff: High frequency cutoff
            filter_order: Filter order
            gui_output: Optional callback for status messages

        Returns:
            Bandpass filtered signal
        """
        if gui_output:
            gui_output(
                f"\nApplying bandpass filter ({low_cutoff}-{high_cutoff}Hz)...")

        # Clamp to valid normalized frequencies (0 < Wn < 1)
        min_low = 0.5
        nyq = float(self.nyquist_freq)
        cl_low = max(min_low, min(float(low_cutoff), nyq - 1.0))
        cl_high = max(cl_low + 0.5, min(float(high_cutoff), nyq - 1.0))

        # Design Butterworth bandpass filter
        b, a = butter(
            filter_order,
            [cl_low / nyq, cl_high / nyq],
            btype='band'
        )

        filtered_signal = np.zeros_like(signal)

        # Apply to all channels
        for ch in range(signal.shape[0]):
            # Check if channel has sufficient samples for filtering
            if signal[ch, :].size >= 10:
                filtered_signal[ch, :] = filtfilt(b, a, signal[ch, :])
            else:
                filtered_signal[ch, :] = signal[ch, :]  # Keep unfiltered if insufficient samples
                if gui_output:
                    gui_output(f"Warning: Channel {ch+1} has insufficient samples ({signal[ch, :].size}) for bandpass filtering")

        if gui_output:
            gui_output("Bandpass filtering complete.")

        return filtered_signal

    def create_cfa_signal(self, signal: np.ndarray,
                          cfa_low: float = CFA_LOW_FREQ,
                          cfa_high: float = CFA_HIGH_FREQ,
                          gui_output: Optional[callable] = None) -> np.ndarray:
        """
        Create conventional frequency analysis (CFA) signal

        Args:
            signal: Input signal (channels x samples)
            cfa_low: CFA low frequency cutoff
            cfa_high: CFA high frequency cutoff
            gui_output: Optional callback for status messages

        Returns:
            CFA filtered signal
        """
        if gui_output:
            gui_output(f"\nCreating CFA signal ({cfa_low}-{cfa_high}Hz)...")

        # First apply notch at 60Hz
        notch_freq = NOTCH_BASE_FREQ
        q_factor = DEFAULT_Q_FACTOR
        b_notch, a_notch = self._create_notch_filter(notch_freq, q_factor)

        # Then apply bandpass for CFA range (clamped)
        nyq = float(self.nyquist_freq)
        cl_cfa_low = max(0.5, min(float(cfa_low), nyq - 1.0))
        cl_cfa_high = max(cl_cfa_low + 0.5, min(float(cfa_high), nyq - 1.0))
        b_bandpass, a_bandpass = butter(
            4,
            [cl_cfa_low / nyq, cl_cfa_high / nyq],
            btype='band'
        )

        cfa_signal = np.zeros_like(signal)

        # Apply both filters to each channel
        for ch in range(signal.shape[0]):
            # Check if channel has sufficient samples for filtering
            if signal[ch, :].size >= 10:
                # Apply notch filter
                temp_signal = filtfilt(b_notch, a_notch, signal[ch, :])
                # Apply bandpass filter
                cfa_signal[ch, :] = filtfilt(b_bandpass, a_bandpass, temp_signal)
            else:
                cfa_signal[ch, :] = signal[ch, :]  # Keep original if insufficient samples

        if gui_output:
            gui_output("CFA signal creation complete.")

        return cfa_signal

    def _calculate_notch_frequencies(self) -> List[float]:
        """
        Calculate notch frequencies (60Hz harmonics)

        Returns:
            List of notch frequencies
        """
        frequencies = []
        base_freq = NOTCH_BASE_FREQ
        harmonic = base_freq

        while harmonic < self.nyquist_freq:
            frequencies.append(harmonic)
            harmonic += base_freq

        return frequencies

    def _create_notch_filter(self, frequency: float,
                             q_factor: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create a second-order IIR notch digital filter

        Args:
            frequency: Notch frequency in Hz
            q_factor: Quality factor

        Returns:
            Tuple of (b, a) filter coefficients
        """
        # Normalize frequency
        wo = frequency / self.nyquist_freq
        bw = wo / q_factor

        # Convert to angular frequency
        wo = wo * np.pi
        bw = bw * np.pi

        # Design notch filter
        # Attenuation at notch frequency
        gb = 10 ** (-3 / 20)  # -3dB attenuation

        # Ensure Gb is valid
        gb = np.clip(gb, 0, 1)

        # Calculate filter parameters
        beta = (np.sqrt(np.abs(1 - gb ** 2)) / gb) * np.tan(bw / 2)
        gain = 1 / (1 + beta)

        # Filter coefficients
        b = gain * np.array([1, -2 * np.cos(wo), 1])
        a = np.array([1, -2 * gain * np.cos(wo), 2 * gain - 1])

        return b, a

    def design_custom_filter(self, filter_type: str,
                             frequencies: List[float],
                             order: int = 4) -> Tuple[np.ndarray, np.ndarray]:
        """
        Design a custom filter

        Args:
            filter_type: Type of filter ('lowpass', 'highpass', 'bandpass', 'bandstop')
            frequencies: Cutoff frequency(ies)
            order: Filter order

        Returns:
            Tuple of (b, a) filter coefficients
        """
        # Normalize frequencies
        if filter_type in ['lowpass', 'highpass']:
            wn = frequencies[0] / self.nyquist_freq
        else:
            wn = [f / self.nyquist_freq for f in frequencies]

        # Design filter
        b, a = butter(order, wn, btype=filter_type)

        return b, a

    def get_filter_response(self, b: np.ndarray, a: np.ndarray,
                            num_points: int = 512) -> Tuple[np.ndarray, np.ndarray]:
        """
        Get frequency response of a filter

        Args:
            b: Numerator coefficients
            a: Denominator coefficients
            num_points: Number of frequency points

        Returns:
            Tuple of (frequencies, magnitude_response)
        """
        from scipy.signal import freqz

        w, h = freqz(b, a, worN=num_points)
        frequencies = w * self.sampling_rate / (2 * np.pi)
        magnitude = np.abs(h)

        return frequencies, magnitude
