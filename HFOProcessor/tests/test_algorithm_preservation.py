"""
Algorithm Preservation Test Suite
Ensures refactored code produces identical results to original implementation
"""

import numpy as np
import json
import hashlib
from typing import Dict, Any, Tuple
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.hfo_engine.hfo_analysis import run_hfo_algorithm


class AlgorithmPreservationTester:
    """Tests to ensure algorithm output remains unchanged after refactoring"""

    @staticmethod
    def generate_test_eeg_data(num_channels: int = 10,
                              duration_seconds: float = 10.0,
                              sampling_rate: float = 500.0) -> Dict[str, Any]:
        """Generate reproducible test EEG data"""
        np.random.seed(42)  # Fixed seed for reproducibility

        num_samples = int(duration_seconds * sampling_rate)

        # Generate realistic EEG-like data with HFOs
        eeg_data = np.zeros((num_channels, num_samples))

        for ch in range(num_channels):
            # Background EEG (1-70 Hz)
            background = np.random.randn(num_samples) * 10

            # Add some HFO-like events (80-500 Hz bursts)
            num_hfos = np.random.randint(5, 15)
            for _ in range(num_hfos):
                hfo_start = np.random.randint(0, num_samples - 100)
                hfo_duration = np.random.randint(10, 50)
                hfo_freq = np.random.uniform(80, 300)
                t = np.arange(hfo_duration) / sampling_rate
                hfo_signal = 5 * np.sin(2 * np.pi * hfo_freq * t)
                eeg_data[ch, hfo_start:hfo_start + hfo_duration] += hfo_signal

            eeg_data[ch] += background

        # Create channel labels
        channel_labels = [f"CH{i+1}" for i in range(num_channels)]

        return {
            'data': eeg_data,
            'nbchan': num_channels,
            'srate': sampling_rate,
            'chanlocs': channel_labels
        }

    @staticmethod
    def create_hash_of_results(results: Dict[str, Any]) -> str:
        """Create a hash of the results for comparison"""
        # Extract key numerical results
        key_values = {
            'num_channels': results.get('num_channels'),
            'success': results.get('success'),
            'counter': np.array(results.get('counter', [])).tolist() if results.get('counter') is not None else [],
            'avg_duration': np.array(results.get('avg_duration', [])).tolist() if results.get('avg_duration') is not None else [],
            'avg_peak_freq': np.array(results.get('avg_peak_freq', [])).tolist() if results.get('avg_peak_freq') is not None else [],
        }

        # Convert to JSON string for consistent hashing
        json_str = json.dumps(key_values, sort_keys=True, default=str)
        return hashlib.md5(json_str.encode()).hexdigest()

    @staticmethod
    def compare_results(result1: Dict[str, Any], result2: Dict[str, Any]) -> Tuple[bool, str]:
        """Compare two result dictionaries for algorithmic equivalence"""
        differences = []

        # Check success status
        if result1.get('success') != result2.get('success'):
            differences.append(f"Success status mismatch: {result1.get('success')} vs {result2.get('success')}")

        # Check channel counts
        if result1.get('num_channels') != result2.get('num_channels'):
            differences.append(f"Channel count mismatch: {result1.get('num_channels')} vs {result2.get('num_channels')}")

        # Check HFO counts per channel
        counter1 = np.array(result1.get('counter', []))
        counter2 = np.array(result2.get('counter', []))
        if not np.array_equal(counter1, counter2):
            differences.append(f"HFO count mismatch: {counter1} vs {counter2}")

        # Check final HFO indices
        final_start1 = result1.get('final_start_ind', [])
        final_start2 = result2.get('final_start_ind', [])

        if len(final_start1) != len(final_start2):
            differences.append(f"Different number of channels with HFOs")
        else:
            for ch_idx in range(len(final_start1)):
                if not np.array_equal(np.array(final_start1[ch_idx]), np.array(final_start2[ch_idx])):
                    differences.append(f"Channel {ch_idx} HFO indices mismatch")

        # Check average characteristics with tolerance
        tolerance = 1e-6
        for field in ['avg_duration', 'avg_peak_freq', 'avg_hfo_power', 'avg_my_amp']:
            val1 = np.array(result1.get(field, []))
            val2 = np.array(result2.get(field, []))
            if val1.shape != val2.shape:
                differences.append(f"{field} shape mismatch")
            elif not np.allclose(val1, val2, rtol=tolerance, atol=tolerance, equal_nan=True):
                differences.append(f"{field} values differ beyond tolerance")

        is_identical = len(differences) == 0
        message = "Results are identical" if is_identical else "\n".join(differences)

        return is_identical, message

    @staticmethod
    def run_comparison_test(eeg_data: Dict[str, Any], parameters: Dict[str, Any]) -> Tuple[bool, str]:
        """Run the algorithm and compare against expected results"""

        # Run original algorithm
        result = run_hfo_algorithm(
            EEG=eeg_data,
            input_file_path="test.edf",
            analysis_start=parameters.get('analysis_start', 0),
            analysis_end=parameters.get('analysis_end', -1),
            montage=parameters.get('montage', 'Bipolar'),
            user_ref=parameters.get('user_ref', None),
            locutoff=parameters.get('low_cutoff', 80),
            hicutoff=parameters.get('high_cutoff', 500),
            gui_output=None,
            threshold_option1=parameters.get('amplitude_1', 3),
            threshold_option2=parameters.get('amplitude_2', 3),
            threshold_option3=parameters.get('peaks_1', 6),
            threshold_option4=parameters.get('peaks_2', 3),
            threshold_option5=parameters.get('duration', 10),
            threshold_option6=parameters.get('temporal_sync', 10),
            threshold_option7=parameters.get('spatial_sync', 10)
        )

        # Store result hash for future comparisons
        result_hash = AlgorithmPreservationTester.create_hash_of_results(result)

        return True, f"Test completed. Result hash: {result_hash}"


def test_basic_hfo_detection():
    """Test basic HFO detection with default parameters"""
    tester = AlgorithmPreservationTester()

    # Generate test data
    eeg_data = tester.generate_test_eeg_data(num_channels=5, duration_seconds=5.0)

    # Define test parameters
    # Note: high_cutoff must be less than nyquist (250 Hz for 500 Hz sampling)
    parameters = {
        'analysis_start': 0,
        'analysis_end': 5,
        'montage': 'Bipolar',
        'low_cutoff': 80,
        'high_cutoff': 200,  # Less than nyquist frequency (250)
        'amplitude_1': 3,
        'amplitude_2': 3,
        'peaks_1': 6,
        'peaks_2': 3,
        'duration': 10,
        'temporal_sync': 10,
        'spatial_sync': 10
    }

    # Run test
    success, message = tester.run_comparison_test(eeg_data, parameters)

    print(f"Basic HFO Detection Test: {'PASSED' if success else 'FAILED'}")
    print(f"Details: {message}")

    return success


def test_different_montages():
    """Test algorithm with different montage types"""
    tester = AlgorithmPreservationTester()
    eeg_data = tester.generate_test_eeg_data(num_channels=8, duration_seconds=3.0)

    montages = ['Bipolar', 'Average', 'Referential']
    results = {}

    for montage in montages:
        parameters = {
            'analysis_start': 0,
            'analysis_end': 3,
            'montage': montage,
            'user_ref': 'CH1' if montage == 'Referential' else None,
            'low_cutoff': 80,
            'high_cutoff': 200,  # Less than nyquist frequency
            'amplitude_1': 3,
            'amplitude_2': 3,
            'peaks_1': 6,
            'peaks_2': 3,
            'duration': 10,
            'temporal_sync': 10,
            'spatial_sync': 10
        }

        success, message = tester.run_comparison_test(eeg_data, parameters)
        results[montage] = (success, message)
        print(f"Montage {montage} Test: {'PASSED' if success else 'FAILED'}")

    return all(r[0] for r in results.values())


def test_different_thresholds():
    """Test algorithm with various threshold parameters"""
    tester = AlgorithmPreservationTester()
    eeg_data = tester.generate_test_eeg_data(num_channels=4, duration_seconds=2.0)

    threshold_sets = [
        {'amplitude_1': 2, 'amplitude_2': 2, 'peaks_1': 4, 'peaks_2': 2},
        {'amplitude_1': 3, 'amplitude_2': 3, 'peaks_1': 6, 'peaks_2': 3},
        {'amplitude_1': 4, 'amplitude_2': 4, 'peaks_1': 8, 'peaks_2': 4},
    ]

    results = []
    for i, thresholds in enumerate(threshold_sets):
        parameters = {
            'analysis_start': 0,
            'analysis_end': 2,
            'montage': 'Bipolar',
            'low_cutoff': 80,
            'high_cutoff': 200,  # Less than nyquist frequency
            **thresholds,
            'duration': 10,
            'temporal_sync': 10,
            'spatial_sync': 10
        }

        success, message = tester.run_comparison_test(eeg_data, parameters)
        results.append(success)
        print(f"Threshold Set {i+1} Test: {'PASSED' if success else 'FAILED'}")

    return all(results)


if __name__ == "__main__":
    print("="*60)
    print("HFO Algorithm Preservation Test Suite")
    print("="*60)

    # Run all tests
    tests_passed = []

    print("\n1. Testing Basic HFO Detection...")
    tests_passed.append(test_basic_hfo_detection())

    print("\n2. Testing Different Montages...")
    tests_passed.append(test_different_montages())

    print("\n3. Testing Different Thresholds...")
    tests_passed.append(test_different_thresholds())

    # Summary
    print("\n" + "="*60)
    print(f"Test Summary: {sum(tests_passed)}/{len(tests_passed)} tests passed")
    print("="*60)

    if all(tests_passed):
        print("✓ All tests passed! Algorithm behavior preserved.")
    else:
        print("✗ Some tests failed. Review algorithm changes.")