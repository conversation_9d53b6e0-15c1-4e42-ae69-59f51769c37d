"""
Parameter Processing Service
Handles analysis parameter preparation and validation
"""

from typing import Any, Dict, Optional


class ParameterProcessor:
    """Processes and validates HFO analysis parameters"""

    @staticmethod
    def prepare_analysis_parameters(parameters: Optional[Dict]) -> Dict:
        """
        Prepare analysis parameters with defaults

        Args:
            parameters: User-provided parameters

        Returns:
            Complete parameters dictionary with defaults
        """
        parameters = parameters or {}
        frequency_params = parameters.get("frequency") or {}
        thresholds_params = parameters.get("thresholds") or {}
        montage_params = parameters.get("montage") or {}

        def _coalesce(keys, *sources, default=None):
            """Find first non-null value from sources"""
            for key in keys:
                for source in sources:
                    if isinstance(source, dict) and key in source:
                        value = source.get(key)
                        if value is not None and value != "":
                            return value
            return default

        def _to_number(value: Any, default: float, cast_type):
            """Convert value to number with default fallback"""
            if value is None:
                return default
            if isinstance(value, (int, float)):
                return cast_type(value)
            try:
                return cast_type(float(value))
            except (TypeError, ValueError):
                return default

        # Analysis window
        analysis_start = _coalesce(
            ("analysis_start", "start_time"), parameters
        )
        analysis_end = _coalesce(
            ("analysis_end", "end_time"), parameters
        )

        # Handle time segment configuration
        time_segment = parameters.get("timeSegment") or parameters.get(
            "time_segment"
        )
        if isinstance(time_segment, dict):
            segment_mode = str(time_segment.get("mode", "")).lower()
            segment_start = _coalesce(
                ("startTime", "start_time"), time_segment
            )
            segment_end = _coalesce(("endTime", "end_time"), time_segment)
            segment_duration = _coalesce(("duration",), time_segment)

            if segment_mode == "entire_file":
                analysis_start = 0 if analysis_start is None else analysis_start
                analysis_end = -1 if analysis_end is None else analysis_end
            elif segment_mode == "start_end_times":
                if segment_start is not None and segment_end is not None:
                    analysis_start = segment_start
                    analysis_end = segment_end
            elif segment_mode == "start_duration":
                if segment_start is not None and segment_duration is not None:
                    analysis_start = segment_start
                    analysis_end = (
                        float(segment_start) + float(segment_duration)
                    )

        resolved_start = _to_number(analysis_start, 0.0, float)
        resolved_end = _to_number(analysis_end, -1.0, float)

        # Special case: when both start and end are 0, treat as "analyze entire file"
        if resolved_start == 0.0 and resolved_end == 0.0:
            resolved_end = -1.0

        # Frequency band parameters
        low_cutoff = _to_number(
            _coalesce(
                (
                    "low_cutoff",
                    "lowCutoff",
                    "low_frequency",
                    "lowFrequency",
                ),
                frequency_params,
                parameters,
            ),
            50.0,
            float,
        )
        high_cutoff = _to_number(
            _coalesce(
                (
                    "high_cutoff",
                    "highCutoff",
                    "high_frequency",
                    "highFrequency",
                ),
                frequency_params,
                parameters,
            ),
            300.0,
            float,
        )

        # Threshold configuration
        amplitude_1 = _to_number(
            _coalesce(
                (
                    "amplitude_1",
                    "amplitude1",
                    "energy_threshold",
                    "threshold_option1",
                ),
                thresholds_params,
                parameters,
            ),
            2.0,
            float,
        )
        amplitude_2 = _to_number(
            _coalesce(
                (
                    "amplitude_2",
                    "amplitude2",
                    "baseline_threshold",
                    "threshold_option2",
                ),
                thresholds_params,
                parameters,
            ),
            2.0,
            float,
        )
        peaks_1 = _to_number(
            _coalesce(
                ("peaks_1", "peaks1", "min_peaks", "threshold_option3"),
                thresholds_params,
                parameters,
            ),
            6,
            int,
        )
        peaks_2 = _to_number(
            _coalesce(
                ("peaks_2", "peaks2", "threshold_option4"),
                thresholds_params,
                parameters,
            ),
            3,
            int,
        )
        duration = _to_number(
            _coalesce(
                ("duration", "min_duration_ms", "threshold_option5"),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )
        temporal_sync = _to_number(
            _coalesce(
                (
                    "temporal_sync",
                    "temporalSync",
                    "threshold_option6",
                ),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )
        spatial_sync = _to_number(
            _coalesce(
                (
                    "spatial_sync",
                    "spatialSync",
                    "threshold_option7",
                ),
                thresholds_params,
                parameters,
            ),
            10,
            int,
        )

        # Montage configuration
        ui_montage = _coalesce(
            ("type", "montage", "montage_type"),
            montage_params,
            parameters,
            default="bipolar",
        )
        montage_map = {
            "bipolar": "Bipolar",
            "average": "Average",
            "referential": "Referential",
        }
        normalized_montage = montage_map.get(
            str(ui_montage).lower(), "Bipolar"
        )

        user_ref = _coalesce(
            ("reference", "reference_channel", "user_ref"),
            montage_params,
            parameters,
        )

        return {
            "analysis_start": resolved_start,
            "analysis_end": resolved_end,
            "montage": normalized_montage,
            "user_ref": user_ref,
            "low_cutoff": low_cutoff,
            "high_cutoff": high_cutoff,
            "amplitude_1": amplitude_1,
            "amplitude_2": amplitude_2,
            "peaks_1": peaks_1,
            "peaks_2": peaks_2,
            "duration": duration,
            "temporal_sync": temporal_sync,
            "spatial_sync": spatial_sync,
        }

    @staticmethod
    def build_parameter_snapshot(
        analysis_params: Dict,
        original_parameters: Optional[Dict],
    ) -> Dict:
        """
        Create a serializable snapshot of the parameters applied to a job

        Args:
            analysis_params: Processed analysis parameters
            original_parameters: Original user-provided parameters

        Returns:
            Parameter snapshot dictionary
        """
        snapshot = {
            "analysis_window_seconds": {
                "start": analysis_params.get("analysis_start"),
                "end": analysis_params.get("analysis_end"),
            },
            "frequency_hz": {
                "low_cutoff": analysis_params.get("low_cutoff"),
                "high_cutoff": analysis_params.get("high_cutoff"),
            },
            "thresholds": {
                "amplitude_1": analysis_params.get("amplitude_1"),
                "amplitude_2": analysis_params.get("amplitude_2"),
                "peaks_1": analysis_params.get("peaks_1"),
                "peaks_2": analysis_params.get("peaks_2"),
                "duration_ms": analysis_params.get("duration"),
                "temporal_sync_ms": analysis_params.get("temporal_sync"),
                "spatial_sync_ms": analysis_params.get("spatial_sync"),
            },
            "montage": analysis_params.get("montage"),
        }

        if analysis_params.get("user_ref"):
            snapshot["reference_channel"] = analysis_params.get("user_ref")

        # Include channel selection if provided
        if isinstance(original_parameters, dict):
            channel_selection = original_parameters.get(
                "channelSelection"
            ) or original_parameters.get("channel_selection")
            if isinstance(channel_selection, dict):
                selected_channels = channel_selection.get(
                    "selectedChannels"
                ) or channel_selection.get("selected_channels")
                if selected_channels:
                    snapshot["channel_selection"] = selected_channels

        return snapshot