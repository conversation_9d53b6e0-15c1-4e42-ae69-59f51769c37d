"""
File Handler Service
Manages file operations for HFO processing
"""

import os
import logging
import numpy as np
from typing import Tu<PERSON>, Dict, Any, Optional
import pyedflib

logger = logging.getLogger(__name__)


class FileHandler:
    """Handles file operations for HFO processing"""

    def __init__(self, temp_dir: str = "/tmp/edf_processing"):
        """
        Initialize file handler

        Args:
            temp_dir: Temporary directory for file operations
        """
        self.temp_dir = temp_dir
        os.makedirs(temp_dir, exist_ok=True)

    def download_file(self, file_key: str, job_id: str, aws_service) -> str:
        """
        Download EDF file from S3

        Args:
            file_key: S3 key of the file
            job_id: Job identifier
            aws_service: AWS service instance

        Returns:
            Local path to downloaded file
        """
        local_path = os.path.join(self.temp_dir, f"{job_id}.edf")
        aws_service.download_file_from_s3(file_key, local_path)
        logger.info(f"Downloaded file {file_key} to {local_path}")
        return local_path

    def load_edf_file(self, local_path: str) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Load EDF file with robust fallback for EDF+D files

        Args:
            local_path: Path to local EDF file

        Returns:
            Tuple of (eeg_data, header_info)
        """
        logger.info(f"Loading EDF file: {local_path}")

        data = None
        signal_labels = None
        srate = 256
        header_info = {}

        try:
            # Try pyedflib first
            edf = pyedflib.EdfReader(local_path)

            n_signals = edf.signals_in_file
            signal_labels = edf.getSignalLabels()
            # Normalize labels to strings
            signal_labels = [
                lbl.decode("utf-8") if isinstance(lbl, (bytes, bytearray)) else lbl
                for lbl in signal_labels
            ]

            sampling_frequencies = [
                edf.getSampleFrequency(i) for i in range(n_signals)
            ]
            srate = int(sampling_frequencies[0]) if sampling_frequencies else 256

            # Stack all channels into a 2D numpy array
            data = np.vstack([edf.readSignal(i) for i in range(n_signals)])

            # Get header info
            header = edf.getHeader()
            start_dt = edf.getStartdatetime()
            header_info = {
                "patientID": header.get("patientname", "Anonymous"),
                "startdate": start_dt.strftime("%d.%m.%y") if start_dt else "",
                "starttime": start_dt.strftime("%H.%M.%S") if start_dt else "",
                "srate": srate,
                "label": signal_labels,
            }
            edf.close()

        except Exception as e:
            # Fall back to binary reader for EDF+D files
            logger.warning(
                f"pyEDFlib failed to read EDF, falling back to binary reader: {e}"
            )
            from core.hfo_engine.edf_reader import read_edf_file as binary_edfread

            hdr, record = binary_edfread(local_path)
            # Ensure proper dtype and shape
            data = record.astype(np.float64, copy=False)

            n_signals = int(data.shape[0])
            # Handle frequency
            freq = hdr.get("frequency")
            if isinstance(freq, (list, np.ndarray)) and len(freq) > 0:
                srate = int(np.round(float(freq[0])))
            elif isinstance(freq, (int, float)):
                srate = int(np.round(float(freq)))
            else:
                srate = 256

            signal_labels = hdr.get("label", [f"CH{i+1}" for i in range(n_signals)])
            header_info = {
                "patientID": hdr.get("patientID", "Anonymous"),
                "startdate": hdr.get("startdate", ""),
                "starttime": hdr.get("starttime", ""),
                "srate": srate,
                "label": signal_labels,
            }

        # Prepare EEG data dictionary
        eeg_data = {
            "data": data,
            "nbchan": int(data.shape[0]),
            "srate": srate,
            "chanlocs": signal_labels,
            "times": None,
        }

        return eeg_data, header_info

    def cleanup_local_files(self, job_id: str):
        """
        Clean up local temporary files

        Args:
            job_id: Job identifier
        """
        try:
            local_path = os.path.join(self.temp_dir, f"{job_id}.edf")
            if os.path.exists(local_path):
                os.remove(local_path)
                logger.info(f"Cleaned up local file for job {job_id}")
        except Exception as e:
            logger.warning(f"Failed to cleanup local files for job {job_id}: {e}")