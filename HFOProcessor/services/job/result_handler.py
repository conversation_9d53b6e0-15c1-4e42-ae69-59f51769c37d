"""
Result Handler Module
Manages HFO analysis result saving and formatting
"""

import os
import json
import csv
import io
import logging
import numpy as np
from typing import Dict, Tuple, Any, Optional

logger = logging.getLogger(__name__)


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles numpy arrays and other numpy types"""

    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(
            obj,
            (
                np.int_,
                np.intc,
                np.intp,
                np.int8,
                np.int16,
                np.int32,
                np.int64,
                np.uint8,
                np.uint16,
                np.uint32,
                np.uint64,
            ),
        ):
            return int(obj)
        if isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            return float(obj)
        if isinstance(obj, (np.complex_, np.complex64, np.complex128)):
            return {"real": obj.real, "imag": obj.imag}
        if isinstance(obj, np.bool_):
            return bool(obj)
        if isinstance(obj, np.void):
            return None
        return super().default(obj)


class ResultHandler:
    """Handles saving and formatting of HFO analysis results"""

    def __init__(self, aws_service):
        """
        Initialize result handler

        Args:
            aws_service: AWS service instance for S3 operations
        """
        self.aws_service = aws_service

    def save_results(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ) -> Tuple[str, str]:
        """
        Save analysis results to S3

        Args:
            job_id: Job identifier
            results: Analysis results
            file_key: Original file S3 key
            parameters: Analysis parameters

        Returns:
            Tuple of (presigned_url, result_key)
        """
        # Save main results JSON
        results_key = f"results/{job_id}/analysis_results.json"
        logger.info(
            f"Saving results to s3://{self.aws_service.s3_bucket_name}/{results_key}"
        )

        self.aws_service.put_object_to_s3(
            json.dumps(results, cls=NumpyEncoder), results_key, "application/json"
        )

        # Generate CSV reports if available
        try:
            from core.hfo_engine.report_generator import REPORT_GENERATOR_AVAILABLE

            if REPORT_GENERATOR_AVAILABLE and results.get("edf_header"):
                self.save_comprehensive_report(
                    job_id, results, file_key, parameters)

            # Save simple HFO events CSV
            if results.get("hfo_events"):
                self.save_hfo_events_csv(job_id, results["hfo_events"])

        except ImportError:
            logger.warning("Report generator not available")

        # Generate presigned URL
        presigned_url = self.aws_service.generate_presigned_url(results_key)

        return presigned_url, results_key

    def save_comprehensive_report(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ):
        """
        Save comprehensive CSV report

        Args:
            job_id: Job identifier
            results: Analysis results
            file_key: Original file S3 key
            parameters: Analysis parameters
        """
        try:
            from core.hfo_engine.report_generator import generate_csv_report

            file_name = os.path.basename(file_key).replace(".edf", "")
            frequency_params = parameters.get("frequency", {})

            csv_report = generate_csv_report(
                parameters=results,
                header=results["edf_header"],
                file_name=file_name,
                locutoff=frequency_params.get("low_cutoff", 80),
                hicutoff=frequency_params.get("high_cutoff", 500),
            )

            report_key = f"results/{job_id}/analysis_report.csv"
            self.aws_service.put_object_to_s3(
                csv_report, report_key, "text/csv")
            logger.info(f"Saved comprehensive report to {report_key}")

        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")

    def save_hfo_events_csv(self, job_id: str, hfo_events: list):
        """
        Save simple CSV of HFO events

        Args:
            job_id: Job identifier
            hfo_events: List of HFO event dictionaries
        """
        output = io.StringIO()
        writer = csv.DictWriter(
            output,
            fieldnames=[
                "channel",
                "start_time",
                "end_time",
                "peak_frequency",
                "amplitude",
            ],
        )
        writer.writeheader()
        writer.writerows(hfo_events)

        csv_key = f"results/{job_id}/hfo_events.csv"
        self.aws_service.put_object_to_s3(
            output.getvalue(), csv_key, "text/csv")
        logger.info(f"Saved HFO events CSV to {csv_key}")

    def format_results_for_storage(
        self,
        results: Dict,
        header_info: Optional[Dict] = None,
        processing_params: Optional[Dict] = None
    ) -> Dict:
        """
        Format results for storage with metadata

        Args:
            results: Raw analysis results
            header_info: EDF header information
            processing_params: Processing parameters used

        Returns:
            Formatted results dictionary
        """
        from datetime import datetime

        formatted = results.copy()

        # Add header info if available
        if header_info:
            formatted["edf_header"] = header_info

        # Add metadata
        metadata = formatted.get("metadata", {})
        metadata["processing_time"] = datetime.utcnow().timestamp()

        if processing_params:
            metadata["parameters_used"] = processing_params

        formatted["metadata"] = metadata

        return formatted