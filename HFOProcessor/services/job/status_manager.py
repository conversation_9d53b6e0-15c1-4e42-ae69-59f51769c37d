"""
Status Manager Module
Manages job status updates with retry logic
"""

import os
import time
import logging
from typing import Dict, Optional, Sequence, Union, Callable
from datetime import datetime

logger = logging.getLogger(__name__)


class StatusManager:
    """Handles job status updates with retry semantics"""

    def __init__(
        self,
        aws_service,
        max_attempts: Optional[int] = None,
        backoff_seconds: Optional[float] = None,
        max_backoff_seconds: Optional[float] = None,
        sleep_func: Optional[Callable[[float], None]] = None
    ):
        """
        Initialize status manager

        Args:
            aws_service: AWS service instance
            max_attempts: Maximum number of retry attempts
            backoff_seconds: Initial backoff time in seconds
            max_backoff_seconds: Maximum backoff time in seconds
            sleep_func: Sleep function for testing
        """
        self.aws_service = aws_service

        # Configure retry parameters
        raw_attempts = (
            max_attempts
            if max_attempts is not None
            else os.getenv("JOB_STATUS_UPDATE_MAX_ATTEMPTS", 3)
        )
        self.max_attempts = max(1, int(raw_attempts))

        raw_backoff = (
            backoff_seconds
            if backoff_seconds is not None
            else float(os.getenv("JOB_STATUS_UPDATE_BACKOFF_SECONDS", 1.0))
        )
        self.backoff_seconds = max(0.0, float(raw_backoff))

        raw_max_backoff = (
            max_backoff_seconds
            if max_backoff_seconds is not None
            else float(os.getenv("JOB_STATUS_UPDATE_MAX_BACKOFF_SECONDS", 15.0))
        )
        self.max_backoff_seconds = max(
            self.backoff_seconds, float(raw_max_backoff)
        )

        self._sleep = sleep_func or time.sleep

    def update_job_status(
        self,
        job_id: str,
        status: str,
        attributes: Optional[Dict] = None,
        expected_status: Optional[Union[str, Sequence[str]]] = None,
        suppress_errors: bool = False,
    ) -> bool:
        """
        Update job status in DynamoDB with retry semantics

        Args:
            job_id: Job identifier
            status: New status
            attributes: Additional attributes to update
            expected_status: Optional set of allowed current statuses for transition
            suppress_errors: When True, swallow errors after retries and return False

        Returns:
            True if successful, False otherwise
        """
        last_error = None

        for attempt in range(1, self.max_attempts + 1):
            try:
                self.aws_service.update_job_status(
                    job_id,
                    status,
                    attributes,
                    expected_status=expected_status,
                )

                if attempt > 1:
                    logger.info(
                        "Updated job %s to status '%s' after %s attempts",
                        job_id,
                        status,
                        attempt,
                    )
                return True

            except Exception as exc:
                last_error = exc
                if attempt == self.max_attempts:
                    break

                wait_time = min(
                    self.backoff_seconds * (2 ** (attempt - 1)),
                    self.max_backoff_seconds,
                )

                logger.warning(
                    "Attempt %s/%s to update job %s to status '%s' failed: %s",
                    attempt,
                    self.max_attempts,
                    job_id,
                    status,
                    exc,
                )

                if wait_time > 0:
                    self._sleep(wait_time)

        if last_error:
            logger.error(
                "Unable to update job %s to status '%s' after %s attempts: %s",
                job_id,
                status,
                self.max_attempts,
                last_error,
            )
            if not suppress_errors:
                raise last_error

        return False

    def mark_job_processing(self, job_id: str) -> bool:
        """
        Mark job as processing

        Args:
            job_id: Job identifier

        Returns:
            True if successful
        """
        return self.update_job_status(
            job_id, "processing", expected_status="pending"
        )

    def mark_job_completed(
        self,
        job_id: str,
        results_url: str,
        result_key: str,
        hfo_count: int = 0,
        parameters_used: Optional[Dict] = None
    ) -> bool:
        """
        Mark job as completed with results

        Args:
            job_id: Job identifier
            results_url: Presigned URL for results
            result_key: S3 key for results
            hfo_count: Number of HFOs detected
            parameters_used: Parameters used for processing

        Returns:
            True if successful
        """
        attributes = {
            "results_url": results_url,
            "result_key": result_key,
            "hfo_count": hfo_count,
            "completed_at": datetime.utcnow().isoformat(),
        }

        if parameters_used:
            attributes["parameters_used"] = parameters_used

        return self.update_job_status(
            job_id,
            "completed",
            attributes,
            expected_status="processing",
        )

    def mark_job_failed(
        self,
        job_id: str,
        error_message: str,
        parameters_used: Optional[Dict] = None,
        suppress_errors: bool = True
    ) -> bool:
        """
        Mark job as failed

        Args:
            job_id: Job identifier
            error_message: Error message
            parameters_used: Parameters that were used
            suppress_errors: Whether to suppress errors

        Returns:
            True if successful
        """
        attributes = {"error_message": error_message}

        if parameters_used:
            attributes["parameters_used"] = parameters_used

        return self.update_job_status(
            job_id,
            "failed",
            attributes,
            expected_status=("processing", "pending"),
            suppress_errors=suppress_errors,
        )

    def get_job_status(self, job_id: str) -> Optional[str]:
        """
        Get current job status

        Args:
            job_id: Job identifier

        Returns:
            Current status or None if not found
        """
        job_details = self.aws_service.get_job_details(job_id)
        if job_details:
            return job_details.get("status")
        return None