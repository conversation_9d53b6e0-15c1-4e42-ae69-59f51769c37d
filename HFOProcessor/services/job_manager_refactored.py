"""
Job Manager Service (Refactored)
Orchestrates HFO analysis job lifecycle using modular components
"""

import os
import json
import logging
import traceback
from typing import Any, Dict, Optional
from .aws_service import AWSService, JobStatusUpdateError
from .notification_service import NotificationService
from .parameter_processor import ParameterProcessor
from .file_handler import <PERSON><PERSON>and<PERSON>
from .job import ResultHandler, StatusManager

logger = logging.getLogger(__name__)


class JobManager:
    """Orchestrates HFO analysis job lifecycle with modular components"""

    def __init__(
        self,
        aws_service: Optional[AWSService] = None,
        notification_service: Optional[NotificationService] = None,
    ):
        """
        Initialize job manager

        Args:
            aws_service: AWS service instance
            notification_service: Notification service instance
        """
        self.aws_service = aws_service or AWSService()
        self.notification_service = notification_service or NotificationService(
            self.aws_service
        )

        # Initialize modular components
        self.status_manager = StatusManager(self.aws_service)
        self.result_handler = ResultHandler(self.aws_service)
        self.parameter_processor = ParameterProcessor()
        self.file_handler = FileHandler("/tmp/edf_processing")

        # Configuration
        raw_receive_count = os.getenv("JOB_MAX_RECEIVE_COUNT", 5)
        self.max_receive_count = max(1, int(raw_receive_count))

    def process_job_message(self, message: Dict) -> bool:
        """
        Process a job message from SQS

        Args:
            message: SQS message dictionary

        Returns:
            True if job processed successfully
        """
        receipt_handle = message.get("ReceiptHandle")
        attributes = message.get("Attributes") or {}
        receive_count = self._extract_receive_count(attributes)
        job_id = None
        user_email = None
        sender_email = None
        file_key = None

        try:
            # Parse message
            body = self._parse_message(message)
            job_id = body.get("job_id")
            file_key = body.get("file_key")
            user_email = body.get("receiver_email") or body.get("user_email")
            sender_email = body.get("sender_email")
            parameters = body.get("parameters", {})

            if not job_id or not file_key:
                raise ValueError(f"Job message missing required fields: job_id={job_id}, file_key={file_key}")

            logger.info(
                "Processing job %s (attempt %s) for file %s",
                job_id,
                receive_count,
                file_key,
            )

            # Update job status to processing
            self.status_manager.mark_job_processing(job_id)

            # Process the job
            success = self.process_job(
                job_id, file_key, parameters, user_email, sender_email
            )

            # Delete message after processing
            self._delete_message_safe(receipt_handle, job_id)

            return success

        except Exception as e:
            error_msg = f"Error processing job message: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)

            # Handle failure
            if job_id:
                self.status_manager.mark_job_failed(
                    job_id,
                    str(e),
                    suppress_errors=True
                )

                if user_email and file_key:
                    self.notification_service.send_error_email(
                        job_id,
                        user_email,
                        file_key,
                        str(e),
                        sender_email=sender_email,
                    )

            # Delete message on error (fail-fast approach)
            if receipt_handle:
                self._delete_message_safe(receipt_handle, job_id or "unknown")

            return False

    def process_job(
        self,
        job_id: str,
        file_key: str,
        parameters: Dict,
        user_email: Optional[str] = None,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Process a single HFO analysis job

        Args:
            job_id: Job identifier
            file_key: S3 key of the EDF file
            parameters: Analysis parameters
            user_email: User email for notifications
            sender_email: Sender email for notifications

        Returns:
            True if job processed successfully
        """
        try:
            # Download file
            local_path = self.file_handler.download_file(
                file_key, job_id, self.aws_service
            )

            # Perform HFO analysis
            results = self._run_hfo_analysis(
                local_path, job_id, parameters
            )

            # Save results
            results_url, result_key = self.result_handler.save_results(
                job_id, results, file_key, parameters
            )

            # Update job status
            self.status_manager.mark_job_completed(
                job_id,
                results_url,
                result_key,
                hfo_count=results.get("statistics", {}).get("total_hfos", 0),
                parameters_used=results.get("metadata", {}).get("parameters_used")
            )

            # Send completion email
            if user_email:
                self.notification_service.send_completion_email(
                    job_id,
                    user_email,
                    file_key,
                    results,
                    sender_email=sender_email,
                )

            # Clean up
            self.file_handler.cleanup_local_files(job_id)

            return True

        except Exception as e:
            logger.error(f"Failed to process job {job_id}: {e}")

            # Update status and notify
            self.status_manager.mark_job_failed(job_id, str(e))

            if user_email:
                self.notification_service.send_error_email(
                    job_id,
                    user_email,
                    file_key,
                    str(e),
                    sender_email=sender_email,
                )

            # Clean up
            self.file_handler.cleanup_local_files(job_id)
            return False

    def _run_hfo_analysis(
        self,
        local_path: str,
        job_id: str,
        parameters: Dict
    ) -> Dict:
        """
        Run HFO analysis on the downloaded file

        Args:
            local_path: Local path to the EDF file
            job_id: Job identifier
            parameters: Analysis parameters

        Returns:
            Analysis results dictionary
        """
        from core.hfo_engine.pipeline import HFODetectionPipeline
        from core.hfo_engine.result_formatter import format_results_for_api

        # Load EDF file
        eeg_data, header_info = self.file_handler.load_edf_file(local_path)

        # Prepare parameters
        analysis_params = self.parameter_processor.prepare_analysis_parameters(parameters)
        parameter_snapshot = self.parameter_processor.build_parameter_snapshot(
            analysis_params, parameters
        )

        logger.info(f"Running HFO detection for job {job_id}")

        # Run detection
        pipeline = HFODetectionPipeline()
        hfo_results = pipeline.run(
            eeg_data=eeg_data,
            file_path=local_path,
            parameters=analysis_params
        )

        if not hfo_results.get("success"):
            raise ValueError(
                f"HFO analysis failed: {hfo_results.get('error', 'Unknown error')}"
            )

        # Format results
        results = format_results_for_api(hfo_results, eeg_data)
        results = self.result_handler.format_results_for_storage(
            results,
            header_info,
            parameter_snapshot
        )

        return results

    def _parse_message(self, message: Dict) -> Dict:
        """
        Parse SQS message body

        Args:
            message: SQS message dictionary

        Returns:
            Parsed message body
        """
        body_raw = message.get("Body")
        if body_raw is None:
            raise ValueError("SQS message missing Body")
        return json.loads(body_raw)

    def _extract_receive_count(self, attributes: Dict[str, Any]) -> int:
        """
        Extract receive count from SQS attributes

        Args:
            attributes: SQS message attributes

        Returns:
            Receive count
        """
        try:
            return int(attributes.get("ApproximateReceiveCount", "1"))
        except (TypeError, ValueError):
            return 1

    def _delete_message_safe(self, receipt_handle: Optional[str], job_id: str) -> bool:
        """
        Safely delete SQS message

        Args:
            receipt_handle: Message receipt handle
            job_id: Job identifier

        Returns:
            True if successful
        """
        if not receipt_handle:
            logger.warning(
                "Cannot delete SQS message for job %s: no receipt handle",
                job_id,
            )
            return False

        success = self.aws_service.delete_sqs_message(receipt_handle)
        if not success:
            logger.warning(
                "Failed to delete SQS message for job %s",
                job_id,
            )
        return success