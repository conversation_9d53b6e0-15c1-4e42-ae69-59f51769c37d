"""
Job Manager Service
Handles HFO analysis job lifecycle management
"""

import os
import json
import logging
import traceback
import time
import numpy as np
from collections.abc import Sequence
from typing import Any, Callable, Dict, Optional, Tuple
from .aws_service import AWSService, JobStatusUpdateError
from .notification_service import NotificationService
from .parameter_processor import ParameterProcessor
from .file_handler import FileHandler

logger = logging.getLogger(__name__)


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles numpy arrays and other numpy types"""

    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(
            obj,
            (
                np.int_,
                np.intc,
                np.intp,
                np.int8,
                np.int16,
                np.int32,
                np.int64,
                np.uint8,
                np.uint16,
                np.uint32,
                np.uint64,
            ),
        ):
            return int(obj)
        if isinstance(obj, (np.float_, np.float16, np.float32, np.float64)):
            return float(obj)
        if isinstance(obj, (np.complex_, np.complex64, np.complex128)):
            return {"real": obj.real, "imag": obj.imag}
        if isinstance(obj, np.bool_):
            return bool(obj)
        if isinstance(obj, np.void):
            return None
        return super().default(obj)


class JobManager:
    """Manages HFO analysis job lifecycle"""

    def __init__(
        self,
        aws_service: Optional[AWSService] = None,
        notification_service: Optional[NotificationService] = None,
        *,
        status_update_max_attempts: Optional[int] = None,
        status_update_backoff_seconds: Optional[float] = None,
        status_update_max_backoff_seconds: Optional[float] = None,
        max_receive_count: Optional[int] = None,
        sleep_func: Optional[Callable[[float], None]] = None,
    ):
        """
        Initialize job manager

        Args:
            aws_service: AWS service instance
            notification_service: Notification service instance
        """
        self.aws_service = aws_service or AWSService()
        self.notification_service = notification_service or NotificationService(
            self.aws_service
        )
        self.temp_dir = "/tmp/edf_processing"
        self.parameter_processor = ParameterProcessor()
        self.file_handler = FileHandler(self.temp_dir)
        raw_attempts = (
            status_update_max_attempts
            if status_update_max_attempts is not None
            else os.getenv("JOB_STATUS_UPDATE_MAX_ATTEMPTS", 3)
        )
        self.status_update_max_attempts = max(1, int(raw_attempts))

        raw_backoff = (
            status_update_backoff_seconds
            if status_update_backoff_seconds is not None
            else float(os.getenv("JOB_STATUS_UPDATE_BACKOFF_SECONDS", 1.0))
        )
        self.status_update_backoff_seconds = max(0.0, float(raw_backoff))

        raw_max_backoff = (
            status_update_max_backoff_seconds
            if status_update_max_backoff_seconds is not None
            else float(os.getenv("JOB_STATUS_UPDATE_MAX_BACKOFF_SECONDS", 15.0))
        )
        self.status_update_max_backoff_seconds = max(
            self.status_update_backoff_seconds, float(raw_max_backoff)
        )

        raw_receive_count = (
            max_receive_count
            if max_receive_count is not None
            else os.getenv("JOB_MAX_RECEIVE_COUNT", 5)
        )
        self.max_receive_count = max(1, int(raw_receive_count))
        self._sleep = sleep_func or time.sleep

    def process_job_message(self, message: Dict) -> bool:
        """
        Process a job message from SQS

        Args:
            message: SQS message dictionary

        Returns:
            True if job processed successfully
        """
        receipt_handle = message.get("ReceiptHandle")
        attributes = message.get("Attributes") or {}
        receive_count = self._extract_receive_count(attributes)
        job_id = None
        user_email = None
        sender_email = None
        file_key = None

        try:
            # Parse message body
            body_raw = message.get("Body")
            if body_raw is None:
                raise ValueError("SQS message missing Body")
            body = json.loads(body_raw)
            job_id = body.get("job_id")
            file_key = body.get("file_key")
            user_email = body.get("receiver_email") or body.get("user_email")
            sender_email = body.get("sender_email")
            parameters = body.get("parameters", {})

            if not job_id:
                raise ValueError("Job message missing job_id")
            if not file_key:
                raise ValueError(f"Job {job_id} missing file_key")

            logger.info(
                "Processing job %s (attempt %s) for file %s",
                job_id,
                receive_count,
                file_key,
            )

            # With fail-fast (max_receive_count=1), this check is mostly defensive
            # Messages should go to DLQ after first failure, but check anyway
            if receive_count > 1:
                error_message = (
                    f"Job unexpectedly received multiple times (count: {receive_count}). "
                    "This should not happen with fail-fast configuration."
                )
                logger.warning(
                    "Job %s received %s times (expected 1 with fail-fast). Processing anyway.",
                    job_id,
                    receive_count,
                )
                # Continue processing but log the unexpected behavior

            # Update job status to processing
            self.update_job_status(
                job_id, "processing", expected_status="pending"
            )

            # Process the job
            success = self.process_job(
                job_id, file_key, parameters, user_email, sender_email)

            # Always delete message after processing (fail-fast approach)
            deleted = self._delete_message_safe(receipt_handle, job_id)

            if success:
                if deleted:
                    logger.info(f"Successfully processed job {job_id}")
                else:
                    logger.warning(
                        "Job %s completed but SQS message could not be deleted",
                        job_id,
                    )
                return True
            else:
                logger.error(f"Failed to process job {job_id}")
                if deleted:
                    logger.info(
                        "Deleted failed job %s from queue (fail-fast, no retries)",
                        job_id,
                    )
                else:
                    logger.warning(
                        "Failed job %s could not be deleted from queue",
                        job_id,
                    )
                return False

        except Exception as e:
            error_msg = (
                f"Error processing job message: {str(e)}\n{traceback.format_exc()}"
            )
            logger.error(error_msg)

            # Update job as failed if we have job_id
            if job_id:
                self.update_job_status(
                    job_id,
                    "failed",
                    {"error_message": str(e)},
                    expected_status=("processing", "pending"),
                    suppress_errors=True,
                )

                # Send error notification if we have user email
                if user_email and file_key:
                    self.notification_service.send_error_email(
                        job_id,
                        user_email,
                        file_key,
                        str(e),
                        sender_email=sender_email,
                    )

            # Always delete message on error (fail-fast approach)
            if receipt_handle:
                deleted = self._delete_message_safe(receipt_handle, job_id or "unknown")
                if deleted:
                    logger.info(
                        "Deleted failed message from queue (fail-fast, no retries)"
                    )
                else:
                    logger.warning(
                        "Failed to delete error message from queue; message may be retried"
                    )

            return False

    def process_job(
        self,
        job_id: str,
        file_key: str,
        parameters: Dict,
        user_email: Optional[str] = None,
        sender_email: Optional[str] = None,
    ) -> bool:
        """
        Process a single HFO analysis job

        Args:
            job_id: Job identifier
            file_key: S3 key of the EDF file
            parameters: Analysis parameters
            user_email: User email for notifications

        Returns:
            True if job processed successfully
        """
        parameter_snapshot: Dict[str, Any] = {}
        try:
            # Download file from S3
            local_path = self.download_file(file_key, job_id)

            # Run HFO analysis
            from core.hfo_engine.pipeline import HFODetectionPipeline
            from core.hfo_engine.result_formatter import format_results_for_api
            from datetime import datetime

            # Load EDF file using FileHandler
            eeg_data, header_info = self.file_handler.load_edf_file(local_path)

            # Prepare analysis parameters
            analysis_params = self.prepare_analysis_parameters(parameters)
            parameter_snapshot = self._build_parameter_snapshot(
                analysis_params, parameters
            )
            logger.info(
                f"Resolved parameters for job {job_id}: {parameter_snapshot}"
            )

            # Run HFO detection
            logger.info(f"Running HFO detection for job {job_id}")
            pipeline = HFODetectionPipeline()
            hfo_results = pipeline.run(
                eeg_data=eeg_data, file_path=local_path, parameters=analysis_params
            )

            if not hfo_results.get("success"):
                raise ValueError(
                    f"HFO analysis failed: {hfo_results.get('error', 'Unknown error')}"
                )

            # Format results
            results = format_results_for_api(hfo_results, eeg_data)
            results["edf_header"] = header_info
            metadata = results.get("metadata", {})
            metadata["processing_time"] = datetime.utcnow().timestamp()
            metadata["parameters_used"] = parameter_snapshot
            if parameters:
                metadata["submitted_parameters"] = parameters
            results["metadata"] = metadata

            # Save results
            results_url, result_key = self.save_results(
                job_id, results, file_key, parameters
            )

            # Update job as completed
            self.update_job_status(
                job_id,
                "completed",
                {
                    "results_url": results_url,
                    "result_key": result_key,
                    "hfo_count": results.get("statistics", {}).get("total_hfos", 0),
                    "completed_at": datetime.utcnow().isoformat(),
                    "parameters_used": parameter_snapshot,
                },
                expected_status="processing",
            )

            # Send completion email
            if user_email:
                self.notification_service.send_completion_email(
                    job_id,
                    user_email,
                    file_key,
                    results,
                    sender_email=sender_email,
                )

            # Clean up local files
            self.cleanup_local_files(job_id)

            return True

        except Exception as e:
            logger.error(f"Failed to process job {job_id}: {e}")
            failure_attributes = {"error_message": str(e)}
            if parameter_snapshot:
                failure_attributes["parameters_used"] = parameter_snapshot
            self.update_job_status(
                job_id,
                "failed",
                failure_attributes,
                expected_status=("processing", "pending"),
            )

            if user_email:
                self.notification_service.send_error_email(
                    job_id,
                    user_email,
                    file_key,
                    str(e),
                    sender_email=sender_email,
                )

            # Clean up on failure
            self.cleanup_local_files(job_id)
            return False

    def download_file(self, file_key: str, job_id: str) -> str:
        """
        Download EDF file from S3

        Args:
            file_key: S3 key of the file
            job_id: Job identifier

        Returns:
            Local path to downloaded file
        """
        return self.file_handler.download_file(file_key, job_id, self.aws_service)

    def save_results(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ) -> Tuple[str, str]:
        """
        Save analysis results to S3

        Args:
            job_id: Job identifier
            results: Analysis results
            file_key: Original file S3 key
            parameters: Analysis parameters

        Returns:
            Tuple of (presigned_url, result_key)
        """
        # Save main results JSON
        results_key = f"results/{job_id}/analysis_results.json"
        logger.info(
            f"Saving results to s3://{self.aws_service.s3_bucket_name}/{results_key}"
        )

        self.aws_service.put_object_to_s3(
            json.dumps(
                results, cls=NumpyEncoder), results_key, "application/json"
        )

        # Generate CSV reports if available
        try:
            from core.hfo_engine.report_generator import REPORT_GENERATOR_AVAILABLE

            if REPORT_GENERATOR_AVAILABLE and results.get("edf_header"):
                self.save_comprehensive_report(
                    job_id, results, file_key, parameters)

            # Save simple HFO events CSV
            if results.get("hfo_events"):
                self.save_hfo_events_csv(job_id, results["hfo_events"])

        except ImportError:
            logger.warning("Report generator not available")

        # Generate presigned URL
        presigned_url = self.aws_service.generate_presigned_url(results_key)

        return presigned_url, results_key

    def save_comprehensive_report(
        self, job_id: str, results: Dict, file_key: str, parameters: Dict
    ):
        """Save comprehensive CSV report"""
        try:
            from core.hfo_engine.report_generator import generate_csv_report

            file_name = os.path.basename(file_key).replace(".edf", "")
            frequency_params = parameters.get("frequency", {})

            csv_report = generate_csv_report(
                parameters=results,
                header=results["edf_header"],
                file_name=file_name,
                locutoff=frequency_params.get("low_cutoff", 80),
                hicutoff=frequency_params.get("high_cutoff", 500),
            )

            report_key = f"results/{job_id}/analysis_report.csv"
            self.aws_service.put_object_to_s3(
                csv_report, report_key, "text/csv")
            logger.info(f"Saved comprehensive report to {report_key}")

        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")

    def save_hfo_events_csv(self, job_id: str, hfo_events: list):
        """Save simple CSV of HFO events"""
        import csv
        import io

        output = io.StringIO()
        writer = csv.DictWriter(
            output,
            fieldnames=[
                "channel",
                "start_time",
                "end_time",
                "peak_frequency",
                "amplitude",
            ],
        )
        writer.writeheader()
        writer.writerows(hfo_events)

        csv_key = f"results/{job_id}/hfo_events.csv"
        self.aws_service.put_object_to_s3(
            output.getvalue(), csv_key, "text/csv")
        logger.info(f"Saved HFO events CSV to {csv_key}")

    def prepare_analysis_parameters(self, parameters: Optional[Dict]) -> Dict:
        """
        Prepare analysis parameters with defaults

        Args:
            parameters: User-provided parameters

        Returns:
            Complete parameters dictionary
        """
        return self.parameter_processor.prepare_analysis_parameters(parameters)

    def _build_parameter_snapshot(
        self,
        analysis_params: Dict,
        original_parameters: Optional[Dict],
    ) -> Dict:
        """Create a serializable snapshot of the parameters applied to a job."""
        return self.parameter_processor.build_parameter_snapshot(
            analysis_params, original_parameters
        )

    def _extract_receive_count(self, attributes: Dict[str, Any]) -> int:
        """Return ApproximateReceiveCount from SQS attributes."""
        try:
            return int(attributes.get("ApproximateReceiveCount", "1"))
        except (TypeError, ValueError):
            return 1

    def _delete_message_safe(self, receipt_handle: Optional[str], job_id: str) -> bool:
        """Best-effort deletion of an SQS message."""
        if not receipt_handle:
            logger.warning(
                "Cannot delete SQS message for job %s because receipt handle is missing",
                job_id,
            )
            return False

        success = self.aws_service.delete_sqs_message(receipt_handle)
        if not success:
            logger.warning(
                "Failed to delete SQS message for job %s; message will become visible again",
                job_id,
            )
        return success

    def update_job_status(
        self,
        job_id: str,
        status: str,
        attributes: Optional[Dict] = None,
        expected_status: Optional[Sequence[str] | str] = None,
        suppress_errors: bool = False,
    ) -> bool:
        """
        Update job status in DynamoDB with retry semantics.

        Args:
            job_id: Job identifier
            status: New status
            attributes: Additional attributes to update
            expected_status: Optional set of allowed current statuses for transition
            suppress_errors: When True, swallow errors after retries and return False
        """
        last_error: Optional[JobStatusUpdateError] = None
        for attempt in range(1, self.status_update_max_attempts + 1):
            try:
                self.aws_service.update_job_status(
                    job_id,
                    status,
                    attributes,
                    expected_status=expected_status,
                )
                if attempt > 1:
                    logger.info(
                        "Updated job %s to status '%s' after %s attempts",
                        job_id,
                        status,
                        attempt,
                    )
                return True
            except JobStatusUpdateError as exc:
                last_error = exc
                if attempt == self.status_update_max_attempts:
                    break
                wait_time = min(
                    self.status_update_backoff_seconds * (2 ** (attempt - 1)),
                    self.status_update_max_backoff_seconds,
                )
                logger.warning(
                    "Attempt %s/%s to update job %s to status '%s' failed: %s",
                    attempt,
                    self.status_update_max_attempts,
                    job_id,
                    status,
                    exc,
                )
                if wait_time > 0:
                    self._sleep(wait_time)

        if last_error:
            logger.error(
                "Unable to update job %s to status '%s' after %s attempts: %s",
                job_id,
                status,
                self.status_update_max_attempts,
                last_error,
            )
            if not suppress_errors:
                raise last_error
        return False

    def cleanup_local_files(self, job_id: str):
        """
        Clean up local temporary files

        Args:
            job_id: Job identifier
        """
        self.file_handler.cleanup_local_files(job_id)
