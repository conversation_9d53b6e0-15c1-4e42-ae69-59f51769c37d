# Bior<PERSON>ka Stack - Brain Activity Analysis Platform

## What is This?

Biormika Stack is a cloud-based platform that helps neurologists and researchers analyze brain recordings (EEG files) to automatically detect High-Frequency Oscillations (HFOs) - important patterns that can indicate epileptic brain tissue.

### The Medical Problem

- **EEG recordings** capture brain activity from multiple sensors (channels) over time
- **HFOs** are brief (6-100ms) high-frequency (80-500Hz) brain oscillations
- **Manual detection** takes hours per file and is inconsistent between analysts
- **Clinical importance**: HFOs help identify seizure onset zones in epilepsy patients

### Our Solution

An automated pipeline that processes EEG files in the cloud:

```
Upload EDF → Queue Job → Detect HFOs → View Results → Email Notification
   (1GB)      (SQS)      (Algorithm)    (Charts)       (Complete)
```

## Architecture Requirements

**IMPORTANT: This project requires ARM64 architecture for optimal performance and cost efficiency**

- **Lambda Functions**: ARM64 (AWS Graviton2) - 20% better price/performance
- **ECS Fargate**: ARM64 containers for HFO processing
- **Docker Builds**: Must use `--platform linux/arm64` flag
- **Python Versions**:
  - Backend/Lambda: Python 3.9+
  - HFOProcessor: Python 3.11-slim specifically
- **AWS Profile**: All commands assume `--profile biormika` configuration

## System Overview

### What Each Component Does

| Component | Purpose | Key Responsibility |
|-----------|---------|-------------------|
| **Frontend** | Web interface | Upload files, view results, interactive charts |
| **Backend** | API server | Coordinate uploads, manage jobs, track status |
| **HFOProcessor** | Detection engine | Run algorithm, find HFOs, generate results |
| **Infrastructure** | Cloud resources | Storage, queue, database, auto-scaling |

### Data Flow

1. **Upload**: User uploads EDF file through web interface
2. **Store**: File saved to S3, job created in database
3. **Queue**: Job sent to SQS for processing
4. **Process**: ECS container downloads file, runs detection
5. **Results**: CSV/JSON saved to S3, email sent
6. **Display**: Frontend shows interactive visualizations

## Quick Start

### Prerequisites

Verify all required tools and configurations:

```bash
# Node.js (20.19+ required)
node --version

# Python (3.9+ for Backend, 3.11 for HFOProcessor)
python3 --version

# Docker with ARM64 support
docker --version
docker buildx ls  # Should show linux/arm64 platform

# AWS CLI with profile configured
aws --version
aws sts get-caller-identity --profile biormika

# AWS CDK
npm install -g aws-cdk
cdk --version

# Verify Docker platform supports ARM64
docker run --rm --platform linux/arm64 alpine uname -m  # Should output: aarch64
```

### First Time Setup

**Important**: Deploy components in order - infrastructure must be deployed first!

```bash
# 1. Deploy infrastructure (creates all AWS resources)
cd Infra
python3 -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
cdk bootstrap --profile biormika  # One-time only
cdk deploy --profile biormika --outputs-file cdk-outputs.json

# IMPORTANT: Note the outputs from cdk-outputs.json
# You'll need these values for Backend configuration

# 2. Configure and start backend
cd ../Backend
python3 -m venv venv && source venv/bin/activate
pip install -r requirements.txt
cp .env.example .env
# Edit .env with values from cdk-outputs.json:
#   - S3_BUCKET_NAME from FilesBucketName
#   - CLOUDFRONT_URL from CloudFrontUrl
python run.py  # http://localhost:8000/docs

# Test health endpoint
curl http://localhost:8000/health

# 3. Start frontend
cd ../Frontend
npm install
npm run dev  # http://localhost:5173

# 4. (Optional) Deploy HFO Processor
cd ../HFOProcessor
./build.sh  # Builds and pushes to ECR (requires Docker)
```

## Essential Commands

### Development
```bash
# Frontend
npm run dev                  # Start dev server (http://localhost:5173)
npm run build               # Build for production
npm run lint                # Check code quality
npx tsc --noEmit           # Check TypeScript types only

# Backend
python run.py               # Start API server (http://localhost:8000/docs)
curl http://localhost:8000/health  # Test health endpoint
./build_lambda.sh          # Package for Lambda (ARM64)

# HFO Processor
docker build --platform linux/arm64 -t hfo .  # Build ARM64 container
python processor.py --test # Test locally
./build.sh                 # Build and push to ECR

# Infrastructure
cdk diff --profile biormika    # Preview changes
cdk deploy --profile biormika  # Apply changes
cdk deploy --profile biormika --outputs-file cdk-outputs.json  # Deploy with outputs
```

### Deployment

**Important**: Deploy in order - infrastructure must be deployed first!

```bash
# Full deployment (in order)
./deploy_infra.sh           # 1. Deploy infrastructure (required first)
./deploy_backend.sh         # 2. Deploy Lambda function
./deploy_frontend.sh        # 3. Deploy React app
./deploy_hfo_processor.sh   # 4. Update HFO container

# Individual component updates
./deploy_backend.sh        # Update API only
./deploy_frontend.sh       # Update web app only (includes CloudFront invalidation)
./deploy_hfo_processor.sh  # Update algorithm only

# Verify deployment
API_URL=$(grep "ApiGatewayUrl" Infra/cdk-outputs.json | cut -d'"' -f4)
curl "${API_URL}health"   # Test deployed API
```

### Monitoring
```bash
# View logs
aws logs tail /ecs/biormika-hfo-processor --profile biormika --follow
aws logs tail /aws/lambda/BiormikaStack-ApiFunction --profile biormika --follow

# Check queue depth
aws sqs get-queue-attributes \
  --queue-url $(aws sqs list-queues --profile biormika | jq -r '.QueueUrls[0]') \
  --attribute-names ApproximateNumberOfMessages \
  --profile biormika

# ECS task status
aws ecs describe-services \
  --cluster biormika-hfo-cluster \
  --services biormika-hfo-processor \
  --profile biormika | jq '.services[0] | {runningCount, desiredCount}'

# Verify ECR repository
aws ecr describe-repositories \
  --repository-names biormika-hfo-processor \
  --profile biormika

# Test Lambda function
aws lambda invoke \
  --function-name BiormikaStack-ApiFunction \
  --cli-binary-format raw-in-base64-out \
  --payload '{"path": "/health", "httpMethod": "GET"}' \
  --profile biormika response.json && cat response.json
```

## Understanding HFO Detection

### The Algorithm

1. **Load EDF**: Read multi-channel brain recordings
2. **Filter**: Apply bandpass filter (80-500 Hz) to isolate HFOs
3. **Detect**: Find segments exceeding threshold (mean + 3σ)
4. **Validate**: Check duration (≥6ms) and merge close events
5. **Output**: Generate CSV with timestamps and amplitudes

### Detection Parameters

```python
{
    "frequency_band": [80, 500],  # Hz - HFO frequency range
    "threshold": 3.0,              # Standard deviations above baseline
    "min_duration_ms": 6,          # Minimum HFO duration
    "min_gap_ms": 10              # Merge events closer than this
}
```

## Project Structure

```
biormika-stack/
├── Frontend/          # React TypeScript web app
├── Backend/           # FastAPI Python API
├── HFOProcessor/      # Detection algorithm (Python)
├── Infra/            # AWS CDK infrastructure (Python)
├── deploy_*.sh       # Deployment scripts
├── CLAUDE.md         # AI assistant context
└── README.md         # This file
```

## Key Technologies

- **Medical**: EDF format, HFO detection, signal processing
- **Frontend**: React, TypeScript, Plotly.js, Redux Toolkit
- **Backend**: FastAPI, Lambda, Boto3, Pydantic
- **Processing**: NumPy, SciPy, PyEDFlib, Docker
- **Cloud**: S3, ECS Fargate, SQS, DynamoDB, CloudFront

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Upload fails | Check S3 CORS, file size <1GB, .edf extension |
| Processing stuck | Check ECS tasks, SQS dead letter queue |
| No results | Verify S3 permissions, check CloudWatch logs |
| CORS errors | Update Backend CORS origins, check CloudFront |

## Environment Variables

### Backend (.env)
```env
S3_BUCKET_NAME=biormika-stack-files-xxxxx
CLOUDFRONT_URL=https://dxxxxx.cloudfront.net
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/xxx
DYNAMODB_TABLE_NAME=BiormikaHFOJobTable
```

### Frontend (vite.config.ts)
```javascript
proxy: {
  '/api': 'http://localhost:8000'  # Development API
}
```

## Performance Specs

- **File Size**: Up to 1GB EDF files
- **Channels**: Processes all channels in parallel
- **Speed**: ~45 seconds per hour of recording
- **Scaling**: 0-10 concurrent processing tasks
- **Memory**: Lambda 3GB, ECS 4GB RAM

## Security Considerations

- EDF files may contain patient data (HIPAA)
- S3 buckets encrypted with AES-256
- All public access blocked
- Presigned URLs expire after 1 hour
- Consider data residency requirements

## Getting Help

- API docs: http://localhost:8000/docs
- Check folder-specific READMEs for details
- Review CLAUDE.md for AI assistance
- AWS costs: Monitor CloudWatch billing alerts

## License

Copyright 2024 Biormika. All rights reserved.