"""
User service for managing user preferences and notifications.
"""

import os
import re
from datetime import datetime
from typing import Any

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)

# Configuration constants
SES_SENDER_EMAIL = os.getenv("SES_SENDER_EMAIL", "<EMAIL>")
DEFAULT_RECEIVER_EMAIL = os.getenv("DEFAULT_RECEIVER_EMAIL", "<EMAIL>")
EMAIL_REGEX = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"


class UserService:
    """Service for user preference and notification management."""

    def __init__(self):
        self.aws = get_aws_clients()
        self.ses = self.aws.ses
        self.preferences_table = self.aws.preferences_table
        self.jobs_table = self.aws.jobs_table

    # Email validation
    @staticmethod
    def validate_email_format(email: str) -> bool:
        """Validate email format."""
        return bool(re.match(EMAIL_REGEX, email))

    # Preference operations
    def get_user_preferences(self, user_id: str) -> dict[str, Any]:
        """Get user preferences from DynamoDB."""
        if not self.preferences_table:
            return {
                "sender_email": SES_SENDER_EMAIL,
                "receiver_email": DEFAULT_RECEIVER_EMAIL,
                "notification_enabled": True,
                "last_updated": datetime.utcnow().isoformat(),
            }

        response = self.preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            return {
                "sender_email": SES_SENDER_EMAIL,
                "receiver_email": "",
                "notification_enabled": True,
                "last_updated": datetime.utcnow().isoformat(),
            }

        item = response["Item"]
        return {
            "sender_email": item.get("sender_email")
            or item.get("email")
            or SES_SENDER_EMAIL,
            "receiver_email": item.get("receiver_email") or item.get("email") or "",
            "notification_enabled": item.get("notification_enabled", True),
            "last_updated": item.get("updated_at", item.get("created_at", "")),
        }

    def update_user_preferences(
        self,
        user_id: str,
        sender_email: str,
        receiver_email: str,
        notification_enabled: bool,
    ) -> dict[str, Any]:
        """Update user preferences in DynamoDB."""
        timestamp = datetime.utcnow().isoformat()

        if not self.preferences_table:
            return {
                "sender_email": sender_email,
                "receiver_email": receiver_email,
                "notification_enabled": notification_enabled,
                "last_updated": timestamp,
            }

        # Check if user exists
        response = self.preferences_table.get_item(Key={"user_id": user_id})

        if "Item" not in response:
            # Create new user
            self.preferences_table.put_item(
                Item={
                    "user_id": user_id,
                    "sender_email": sender_email,
                    "receiver_email": receiver_email,
                    "email": receiver_email,
                    "notification_enabled": notification_enabled,
                    "created_at": timestamp,
                    "updated_at": timestamp,
                }
            )
        else:
            # Update existing user
            self.preferences_table.update_item(
                Key={"user_id": user_id},
                UpdateExpression=(
                    "SET sender_email = :sender, receiver_email = :receiver, "
                    "email = :receiver, notification_enabled = :enabled, updated_at = :updated"
                ),
                ExpressionAttributeValues={
                    ":sender": sender_email,
                    ":receiver": receiver_email,
                    ":enabled": notification_enabled,
                    ":updated": timestamp,
                },
            )

        logger.info(f"Updated preferences for user {user_id}")

        return {
            "sender_email": sender_email,
            "receiver_email": receiver_email,
            "notification_enabled": notification_enabled,
            "last_updated": timestamp,
        }

    # Email operations
    def send_verification_email(self, email: str) -> dict[str, str]:
        """Trigger SES identity verification for the supplied email."""
        try:
            attrs = self.ses.get_identity_verification_attributes(Identities=[email])
            verification_attrs = attrs.get("VerificationAttributes", {})
            status = verification_attrs.get(email, {}).get("VerificationStatus")

            if status == "Success":
                return {
                    "status": "success",
                    "message": "Email address is already verified with SES.",
                }

            self.ses.verify_email_identity(EmailAddress=email)
            logger.info(f"Requested SES verification for {email}")

            return {
                "status": "pending",
                "message": "Verification email sent by AWS SES. Please check your inbox to confirm.",
            }

        except ClientError as e:
            error_code = e.response["Error"].get("Code", "Unknown")
            error_message = e.response["Error"].get("Message", "")
            logger.error(f"SES verification error: {error_code} - {error_message}")

            if error_code in {"TooManyRequestsException", "LimitExceeded"}:
                raise ValueError(
                    "Too many verification attempts. Please try again later."
                ) from e

            if error_code == "AccessDenied":
                raise ValueError(
                    "AWS SES is not permitted to verify this email. Check IAM permissions and region configuration."
                ) from e

            raise ValueError(
                f"Failed to request verification: {error_message or error_code}"
            ) from e

    # Statistics
    def get_user_statistics(self, user_id: str) -> dict[str, int]:
        """Get user analysis statistics."""
        try:
            # For now, return empty stats matching frontend expectations
            # In production, would query jobs_table with user GSI
            return {
                "total_analyses": 0,
                "hfos_detected": 0,
            }

        except Exception as e:
            logger.error(f"Error getting user stats: {e}")
            return {
                "total_analyses": 0,
                "hfos_detected": 0,
            }
