"""
SQS service for job queue management.
"""

import json

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)

MAX_BATCH_SQS_MESSAGES = 10


class SQSService:
    """Manages SQS queue operations for job processing."""

    def __init__(self):
        self.aws = get_aws_clients()

    def create_sqs_message(
        self,
        job_id: str,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None,
        batch_id: str | None = None,
    ) -> dict:
        """Create an SQS message for job processing."""
        message = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "parameters": parameters or {},
        }
        if batch_id:
            message["batch_id"] = batch_id
        return message

    def send_single_message(self, message: dict) -> bool:
        """Send a single message to SQS queue."""
        if not self.aws.sqs_queue_url:
            logger.warning("SQS queue URL not configured")
            return False

        try:
            self.aws.sqs.send_message(
                QueueUrl=self.aws.sqs_queue_url,
                MessageBody=json.dumps(message),
            )
            return True
        except Exception as e:
            logger.error(f"Error sending message to SQS: {e}")
            return False

    def send_batch_messages(self, messages: list[dict]) -> bool:
        """Send multiple messages to SQS queue in batches."""
        if not self.aws.sqs_queue_url or not messages:
            return False

        try:
            # Process in batches (SQS limit is 10)
            for i in range(0, len(messages), MAX_BATCH_SQS_MESSAGES):
                batch = messages[i : i + MAX_BATCH_SQS_MESSAGES]

                # Format messages for batch send
                entries = []
                for msg in batch:
                    if isinstance(msg, dict) and "Id" in msg and "MessageBody" in msg:
                        # Already formatted for batch send
                        entries.append(msg)
                    else:
                        # Need to format the message
                        entries.append(
                            {
                                "Id": msg.get("job_id", str(i)),
                                "MessageBody": json.dumps(msg)
                                if not isinstance(msg.get("MessageBody"), str)
                                else msg["MessageBody"],
                            }
                        )

                self.aws.sqs.send_message_batch(
                    QueueUrl=self.aws.sqs_queue_url,
                    Entries=entries,
                )
            return True
        except Exception as e:
            logger.error(f"Error sending batch messages to SQS: {e}")
            return False

    def prepare_batch_entries(self, job_data_list: list[tuple]) -> list[dict]:
        """
        Prepare batch entries for SQS send_message_batch.

        Args:
            job_data_list: List of tuples containing (job_id, file_key, sender_email, receiver_email, parameters, batch_id)

        Returns:
            List of formatted SQS batch entries
        """
        entries = []
        for job_data in job_data_list:
            job_id, file_key, sender_email, receiver_email, parameters, batch_id = (
                job_data
            )
            message = self.create_sqs_message(
                job_id, file_key, sender_email, receiver_email, parameters, batch_id
            )
            entries.append(
                {
                    "Id": job_id,
                    "MessageBody": json.dumps(message),
                }
            )
        return entries
