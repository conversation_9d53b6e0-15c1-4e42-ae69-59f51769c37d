"""
Unified service for HFO analysis job management.
Handles job lifecycle, DynamoDB operations, and batch processing.
"""

import uuid
from datetime import datetime

from boto3.dynamodb.conditions import Key
from botocore.exceptions import ClientError

from ..logging_config import get_logger
from ..utils.dynamodb_utils import sanitize_for_dynamodb
from .aws_clients import get_aws_clients

logger = get_logger(__name__)


class JobService:
    """Manages HFO analysis job lifecycle and persistence."""

    def __init__(self):
        self.aws = get_aws_clients()

    # Job creation and persistence
    def create_job(
        self,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
    ) -> str:
        """Create and persist a new analysis job."""
        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()

        job_item = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": parameters or {},
        }

        if batch_id:
            job_item["batch_id"] = batch_id

        # Save to DynamoDB
        if self.aws.jobs_table:
            self.aws.jobs_table.put_item(Item=sanitize_for_dynamodb(job_item))
            logger.info(f"Created job: {job_id}")

        return job_id

    def generate_batch_id(self) -> str:
        """Generate unique batch identifier."""
        return str(uuid.uuid4())

    # Job retrieval
    def get_job_by_id(self, job_id: str) -> dict | None:
        """Retrieve job details from DynamoDB."""
        if not self.aws.jobs_table:
            return None

        response = self.aws.jobs_table.get_item(Key={"job_id": job_id})
        return response.get("Item")

    def get_batch_jobs(self, batch_id: str) -> list[dict]:
        """Retrieve all jobs in a batch."""
        if not self.aws.jobs_table:
            return []

        response = self.aws.jobs_table.query(
            IndexName="BatchJobIndex",
            KeyConditionExpression=Key("batch_id").eq(batch_id),
        )
        return response.get("Items", [])

    def get_user_jobs(self, user_email: str | None = None) -> list[dict]:
        """Retrieve jobs, optionally filtered by user email."""
        if not self.aws.jobs_table:
            logger.warning("Jobs table not available")
            return []

        try:
            if user_email:
                # Query by user email using GSI
                response = self.aws.jobs_table.query(
                    IndexName="UserEmailIndex",
                    KeyConditionExpression=Key("user_email").eq(user_email),
                    ScanIndexForward=False,
                )
                jobs = response.get("Items", [])

                # Handle pagination
                while "LastEvaluatedKey" in response:
                    response = self.aws.jobs_table.query(
                        IndexName="UserEmailIndex",
                        KeyConditionExpression=Key("user_email").eq(user_email),
                        ScanIndexForward=False,
                        ExclusiveStartKey=response["LastEvaluatedKey"],
                    )
                    jobs.extend(response.get("Items", []))

                logger.info(f"Found {len(jobs)} jobs for user {user_email}")
                return jobs

            # No filter - scan entire table
            response = self.aws.jobs_table.scan()
            jobs = response.get("Items", [])

            # Handle pagination
            while "LastEvaluatedKey" in response:
                response = self.aws.jobs_table.scan(
                    ExclusiveStartKey=response["LastEvaluatedKey"]
                )
                jobs.extend(response.get("Items", []))

            # Sort by creation date
            jobs.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            logger.info(f"Found {len(jobs)} total jobs")
            return jobs

        except Exception as e:
            logger.error(f"Error retrieving jobs: {e}")
            return []

    # Batch operations
    def summarize_batch_status(self, jobs: list[dict]) -> dict:
        """Generate summary statistics for batch jobs."""
        total = len(jobs)
        status_counts = {
            "completed": sum(1 for j in jobs if j["status"] == "completed"),
            "failed": sum(1 for j in jobs if j["status"] == "failed"),
            "processing": sum(1 for j in jobs if j["status"] == "processing"),
            "pending": sum(1 for j in jobs if j["status"] == "pending"),
        }

        return {
            "total_jobs": total,
            **status_counts,
            "jobs": [
                {
                    "job_id": j["job_id"],
                    "file_key": j.get("file_key"),
                    "status": j["status"],
                    "hfo_count": j.get("hfo_count"),
                    "error": j.get("error_message"),
                }
                for j in jobs
            ],
        }

    # File validation
    def validate_file_exists(self, file_key: str) -> bool:
        """Verify file exists in S3 bucket."""
        try:
            self.aws.s3.head_object(Bucket=self.aws.s3_bucket_name, Key=file_key)
            return True
        except ClientError:
            return False
