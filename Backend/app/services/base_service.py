"""
Base service class providing common functionality for all services.
"""

from typing import Any

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)


class BaseService:
    """Base service with common functionality for all services."""

    def __init__(self):
        """Initialize base service with AWS clients."""
        self.aws = get_aws_clients()
        self._init_service_specific()

    def _init_service_specific(self):
        """Override in child classes for service-specific initialization."""
        pass

    # Common error handling
    @staticmethod
    def handle_client_error(error: ClientError, operation: str, context: str | None = None) -> None:
        """Handle AWS client errors with consistent logging."""
        error_code = error.response.get("Error", {}).get("Code", "Unknown")
        error_message = error.response.get("Error", {}).get("Message", str(error))

        log_msg = f"{operation} failed"
        if context:
            log_msg += f" for {context}"
        log_msg += f": {error_code} - {error_message}"

        logger.error(log_msg)
        raise

    @staticmethod
    def safe_get_item(table, key: dict[str, Any]) -> dict | None:
        """Safely get item from DynamoDB table."""
        if not table:
            return None

        try:
            response = table.get_item(Key=key)
            return response.get("Item")
        except ClientError as e:
            logger.error(f"Error getting item from DynamoDB: {e}")
            return None

    @staticmethod
    def safe_put_item(table, item: dict[str, Any]) -> bool:
        """Safely put item to DynamoDB table."""
        if not table:
            return False

        try:
            table.put_item(Item=item)
            return True
        except ClientError as e:
            logger.error(f"Error putting item to DynamoDB: {e}")
            return False

    # S3 operations helpers
    def file_exists_in_s3(self, key: str) -> bool:
        """Check if file exists in S3."""
        try:
            self.aws.s3.head_object(Bucket=self.aws.s3_bucket_name, Key=key)
            return True
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                return False
            self.handle_client_error(e, "Check file existence", key)
            return False

    def generate_presigned_url(
        self,
        key: str,
        expiry: int = 3600,
        response_content_disposition: str | None = None
    ) -> str:
        """Generate presigned URL for S3 object."""
        params = {
            "Bucket": self.aws.s3_bucket_name,
            "Key": key
        }

        if response_content_disposition:
            params["ResponseContentDisposition"] = response_content_disposition

        return self.aws.s3.generate_presigned_url(
            "get_object",
            Params=params,
            ExpiresIn=expiry
        )

    # DynamoDB query helpers
    def query_by_index(
        self,
        table,
        index_name: str,
        key_condition,
        limit: int | None = None,
        scan_forward: bool = False
    ) -> list[dict]:
        """Query DynamoDB table by index."""
        if not table:
            return []

        try:
            query_params = {
                "IndexName": index_name,
                "KeyConditionExpression": key_condition,
                "ScanIndexForward": scan_forward
            }

            if limit:
                query_params["Limit"] = limit

            response = table.query(**query_params)
            items = response.get("Items", [])

            # Handle pagination if needed
            while "LastEvaluatedKey" in response and not limit:
                query_params["ExclusiveStartKey"] = response["LastEvaluatedKey"]
                response = table.query(**query_params)
                items.extend(response.get("Items", []))

            return items

        except ClientError as e:
            logger.error(f"Error querying DynamoDB index {index_name}: {e}")
            return []
