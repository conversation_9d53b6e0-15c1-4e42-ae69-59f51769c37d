"""
Service for managing analysis results from S3.
"""

import json
import re

from botocore.exceptions import ClientError

from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)

PRESIGNED_URL_EXPIRY = 3600


class ResultsService:
    """Service for managing analysis results."""

    def __init__(self):
        self.aws = get_aws_clients()

    # Core results retrieval
    def get_results_from_s3(self, job_id: str) -> dict:
        """Get full analysis results from S3."""
        results_key = f"results/{job_id}/analysis_results.json"

        response = self.aws.s3.get_object(
            Bucket=self.aws.s3_bucket_name, Key=results_key
        )
        results = json.loads(response["Body"].read())

        # Add download URLs
        results["download_urls"] = {
            "results_json": self._generate_presigned_url(results_key),
            "hfo_events_csv": self._generate_presigned_url(
                f"results/{job_id}/hfo_events.csv"
            ),
        }
        return results

    def get_results_metadata_with_urls(self, job_id: str) -> dict:
        """
        Get analysis results metadata with presigned URLs.
        Returns lightweight metadata to avoid API Gateway payload limits.
        """
        results_key = f"results/{job_id}/analysis_results.json"

        try:
            # Get file metadata
            head_response = self.aws.s3.head_object(
                Bucket=self.aws.s3_bucket_name, Key=results_key
            )

            file_size = head_response.get("ContentLength", 0)
            last_modified = (
                head_response.get("LastModified", "").isoformat()
                if head_response.get("LastModified")
                else None
            )

            # Fetch partial content for metadata
            metadata, statistics = self._extract_metadata_from_s3(results_key)

            # Generate presigned URLs
            data_urls = {
                "full_results": self._generate_presigned_url(results_key),
                "hfo_events_csv": self._generate_presigned_url(
                    f"results/{job_id}/hfo_events.csv"
                ),
                "analysis_report_csv": self._generate_presigned_url(
                    f"results/{job_id}/analysis_report.csv"
                ),
            }

            return {
                "job_id": job_id,
                "file_size_bytes": file_size,
                "last_modified": last_modified,
                "metadata": metadata,
                "statistics": statistics,
                "data_urls": data_urls,
                "message": "Use the data_urls to fetch full results directly from S3",
            }

        except ClientError as e:
            if e.response["Error"]["Code"] == "NoSuchKey":
                raise ValueError(f"Results not found for job {job_id}") from e
            raise

    # Legacy method for backward compatibility
    async def get_results(self, job_id: str, job_status: str) -> dict | None:
        """Get analysis results for a job (legacy method)."""
        if job_status != "completed":
            return None

        try:
            return self.get_results_from_s3(job_id)
        except ClientError:
            logger.error(f"Results not found for job {job_id}")
            return None

    # URL generation
    def generate_download_url(self, job_id: str, format: str = "json") -> str:
        """Generate presigned URL for downloading results."""
        # Determine file key and filename based on format
        if format == "csv":
            key = f"results/{job_id}/hfo_events.csv"
            filename = f"{job_id}_hfo_events.csv"
        elif format == "report":
            key = f"results/{job_id}/analysis_report.csv"
            filename = f"{job_id}_comprehensive_report.csv"
        else:
            key = f"results/{job_id}/analysis_results.json"
            filename = f"{job_id}_results.json"

        return self.aws.s3.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": self.aws.s3_bucket_name,
                "Key": key,
                "ResponseContentDisposition": f'attachment; filename="{filename}"',
            },
            ExpiresIn=PRESIGNED_URL_EXPIRY,
        )

    # Private helper methods
    def _extract_metadata_from_s3(self, results_key: str) -> tuple[dict, dict]:
        """Extract metadata and statistics from partial S3 content."""
        metadata = {}
        statistics = {}

        try:
            # Get first 50KB for metadata
            response = self.aws.s3.get_object(
                Bucket=self.aws.s3_bucket_name,
                Key=results_key,
                Range="bytes=0-50000",
            )

            partial_content = response["Body"].read().decode("utf-8")

            # Parse metadata from partial content
            metadata_match = re.search(r'"metadata":\s*({[^}]+})', partial_content)
            statistics_match = re.search(r'"statistics":\s*({[^}]+})', partial_content)

            if metadata_match:
                metadata = json.loads(metadata_match.group(1))
            if statistics_match:
                statistics = json.loads(statistics_match.group(1))

        except (json.JSONDecodeError, AttributeError) as e:
            logger.warning(f"Could not parse metadata from results file: {e}")

        return metadata, statistics

    def _generate_presigned_url(self, key: str) -> str:
        """Generate a presigned URL for S3 object."""
        return self.aws.s3.generate_presigned_url(
            "get_object",
            Params={"Bucket": self.aws.s3_bucket_name, "Key": key},
            ExpiresIn=PRESIGNED_URL_EXPIRY,
        )
