"""
Analysis service for HFO processing job orchestration.
Integrates job management, submission, and results retrieval.
"""

import json
import uuid
from datetime import datetime

from boto3.dynamodb.conditions import Key

from ..logging_config import get_logger
from ..utils.dynamodb_utils import sanitize_for_dynamodb
from .base_service import BaseService
from .email_service import EmailService
from .results_service import ResultsService

logger = get_logger(__name__)

MAX_BATCH_SQS_MESSAGES = 10


class AnalysisService(BaseService):
    """Service for orchestrating HFO analysis jobs."""

    def _init_service_specific(self):
        """Initialize service-specific dependencies."""
        self.results_service = ResultsService()
        self.email_service = EmailService()

    # Job submission
    async def submit_single_job(
        self,
        file_key: str,
        parameters: dict | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Submit a single analysis job."""
        # Validate file
        if not self.file_exists_in_s3(file_key):
            raise ValueError(f"File not found: {file_key}")

        # Get email preferences
        sender_email, receiver_email = await self.email_service.get_email_preferences(
            "default_user"
        )
        if user_email:
            receiver_email = user_email

        # Create and save job
        job_id = self._create_job(
            file_key, sender_email, receiver_email, parameters
        )

        # Send to SQS
        message = self._create_sqs_message(
            job_id, file_key, sender_email, receiver_email, parameters
        )
        self._send_single_message(message)

        logger.info(f"Submitted analysis job: {job_id}")
        return {
            "job_id": job_id,
            "status": "pending",
            "message": "Analysis job submitted successfully",
        }

    async def submit_batch_jobs(
        self,
        file_keys: list[str],
        parameters: dict[str, dict | None] | None = None,
        user_email: str | None = None,
    ) -> dict:
        """Submit multiple analysis jobs as a batch."""
        # Validate all files
        invalid_files = [
            key for key in file_keys if not self.file_exists_in_s3(key)
        ]
        if invalid_files:
            raise ValueError(f"Files not found: {', '.join(invalid_files)}")

        # Get email preferences
        sender_email, receiver_email = await self.email_service.get_email_preferences(
            "default_user"
        )
        if user_email:
            receiver_email = user_email

        # Create batch
        batch_id = str(uuid.uuid4())
        job_data_list = []

        for file_key in file_keys:
            file_params = parameters.get(file_key) if parameters else None

            # Create and save job
            job_id = self._create_job(
                file_key, sender_email, receiver_email, file_params, batch_id
            )

            # Collect data for batch message
            job_data_list.append(
                (job_id, file_key, sender_email, receiver_email, file_params, batch_id)
            )

        # Send batch messages to SQS
        messages = self._prepare_batch_entries(job_data_list)
        self._send_batch_messages(messages)

        job_ids = [data[0] for data in job_data_list]
        logger.info(f"Submitted batch analysis: {batch_id} with {len(job_ids)} jobs")

        return {
            "batch_id": batch_id,
            "job_ids": job_ids,
            "status": "pending",
            "message": f"Batch analysis submitted with {len(job_ids)} files",
        }

    # Job management
    def _create_job(
        self,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None = None,
        batch_id: str | None = None,
    ) -> str:
        """Create and persist a new analysis job."""
        job_id = str(uuid.uuid4())
        timestamp = datetime.utcnow().isoformat()

        job_item = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "status": "pending",
            "created_at": timestamp,
            "updated_at": timestamp,
            "parameters": parameters or {},
        }

        if batch_id:
            job_item["batch_id"] = batch_id

        # Save to DynamoDB
        if self.aws.jobs_table:
            self.safe_put_item(self.aws.jobs_table, sanitize_for_dynamodb(job_item))
            logger.info(f"Created job: {job_id}")

        return job_id

    # SQS operations
    def _create_sqs_message(
        self,
        job_id: str,
        file_key: str,
        sender_email: str,
        receiver_email: str,
        parameters: dict | None,
        batch_id: str | None = None,
    ) -> dict:
        """Create an SQS message for job processing."""
        message = {
            "job_id": job_id,
            "file_key": file_key,
            "user_email": receiver_email,
            "receiver_email": receiver_email,
            "sender_email": sender_email,
            "parameters": parameters or {},
        }
        if batch_id:
            message["batch_id"] = batch_id
        return message

    def _send_single_message(self, message: dict) -> bool:
        """Send a single message to SQS queue."""
        if not self.aws.sqs_queue_url:
            logger.warning("SQS queue URL not configured")
            return False

        try:
            self.aws.sqs.send_message(
                QueueUrl=self.aws.sqs_queue_url,
                MessageBody=json.dumps(message),
            )
            return True
        except Exception as e:
            logger.error(f"Error sending message to SQS: {e}")
            return False

    def _send_batch_messages(self, messages: list[dict]) -> bool:
        """Send multiple messages to SQS queue in batches."""
        if not self.aws.sqs_queue_url or not messages:
            return False

        try:
            # Process in batches (SQS limit is 10)
            for i in range(0, len(messages), MAX_BATCH_SQS_MESSAGES):
                batch = messages[i : i + MAX_BATCH_SQS_MESSAGES]
                self.aws.sqs.send_message_batch(
                    QueueUrl=self.aws.sqs_queue_url,
                    Entries=batch,
                )
            return True
        except Exception as e:
            logger.error(f"Error sending batch messages to SQS: {e}")
            return False

    def _prepare_batch_entries(self, job_data_list: list[tuple]) -> list[dict]:
        """Prepare batch entries for SQS send_message_batch."""
        entries = []
        for job_data in job_data_list:
            job_id, file_key, sender_email, receiver_email, parameters, batch_id = (
                job_data
            )
            message = self._create_sqs_message(
                job_id, file_key, sender_email, receiver_email, parameters, batch_id
            )
            entries.append(
                {
                    "Id": job_id,
                    "MessageBody": json.dumps(message),
                }
            )
        return entries

    # Job retrieval
    def get_job_by_id(self, job_id: str) -> dict | None:
        """Get job details from DynamoDB."""
        return self.safe_get_item(self.aws.jobs_table, {"job_id": job_id})

    def get_batch_jobs(self, batch_id: str) -> list[dict]:
        """Get all jobs in a batch."""
        if not self.aws.jobs_table:
            return []

        return self.query_by_index(
            self.aws.jobs_table,
            "BatchJobIndex",
            Key("batch_id").eq(batch_id)
        )

    def get_user_jobs(self, user_email: str | None = None) -> list[dict]:
        """Get jobs from DynamoDB."""
        if not self.aws.jobs_table:
            logger.warning("Jobs table not available")
            return []

        try:
            if user_email:
                # Query by user email using GSI
                jobs = self.query_by_index(
                    self.aws.jobs_table,
                    "UserEmailIndex",
                    Key("user_email").eq(user_email),
                    scan_forward=False
                )
                logger.info(f"Found {len(jobs)} jobs for user {user_email}")
                return jobs

            # No filter - scan entire table
            response = self.aws.jobs_table.scan()
            jobs = response.get("Items", [])

            # Handle pagination
            while "LastEvaluatedKey" in response:
                response = self.aws.jobs_table.scan(
                    ExclusiveStartKey=response["LastEvaluatedKey"]
                )
                jobs.extend(response.get("Items", []))

            # Sort by creation date
            jobs.sort(key=lambda x: x.get("created_at", ""), reverse=True)
            logger.info(f"Found {len(jobs)} total jobs")
            return jobs

        except Exception as e:
            logger.error(f"Error retrieving jobs: {e}")
            return []

    # Batch status
    def summarize_batch_status(self, jobs: list[dict]) -> dict:
        """Summarize the status of batch jobs."""
        total = len(jobs)
        status_counts = {
            "completed": sum(1 for j in jobs if j["status"] == "completed"),
            "failed": sum(1 for j in jobs if j["status"] == "failed"),
            "processing": sum(1 for j in jobs if j["status"] == "processing"),
            "pending": sum(1 for j in jobs if j["status"] == "pending"),
        }

        return {
            "total_jobs": total,
            **status_counts,
            "jobs": [
                {
                    "job_id": j["job_id"],
                    "file_key": j.get("file_key"),
                    "status": j["status"],
                    "hfo_count": j.get("hfo_count"),
                    "error": j.get("error_message"),
                }
                for j in jobs
            ],
        }

    # Results retrieval
    def get_results_from_s3(self, job_id: str) -> dict:
        """Get analysis results from S3."""
        return self.results_service.get_results_from_s3(job_id)

    def get_results_metadata_with_urls(self, job_id: str) -> dict:
        """Get analysis results metadata with presigned URLs."""
        return self.results_service.get_results_metadata_with_urls(job_id)

    def generate_download_url(self, job_id: str, format: str = "json") -> str:
        """Generate presigned URL for downloading results."""
        return self.results_service.generate_download_url(job_id, format)
