"""
Centralized email preference and notification service.
"""

import os
import re

from ..config import settings
from ..logging_config import get_logger
from .aws_clients import get_aws_clients

logger = get_logger(__name__)

EMAIL_REGEX = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"


class EmailService:
    """Manages email preferences and configurations."""

    def __init__(self):
        self.aws = get_aws_clients()
        self.default_sender = settings.ses_sender_email
        self.default_receiver = os.getenv(
            "DEFAULT_RECEIVER_EMAIL", "<EMAIL>"
        )

    def validate_email_format(self, email: str) -> bool:
        """Validate email address format."""
        return bool(re.match(EMAIL_REGEX, email))

    async def get_email_preferences(self, user_id: str) -> tuple[str, str]:
        """Get sender and receiver email addresses from preferences table."""
        sender_email = self.default_sender
        receiver_email = self.default_receiver

        try:
            if self.aws.preferences_table:
                response = self.aws.preferences_table.get_item(Key={"user_id": user_id})
                item = response.get("Item")
                if item:
                    sender_email = (
                        item.get("sender_email")
                        or item.get("email")
                        or self.default_sender
                    )
                    receiver_email = (
                        item.get("receiver_email")
                        or item.get("email")
                        or self.default_receiver
                    )
        except Exception as e:
            logger.warning(f"Error getting user email preferences: {e}")

        return sender_email, receiver_email

    def get_default_emails(self) -> tuple[str, str]:
        """Get default sender and receiver emails."""
        return self.default_sender, self.default_receiver
