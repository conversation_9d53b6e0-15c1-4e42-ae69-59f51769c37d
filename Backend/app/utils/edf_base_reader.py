"""
Base EDF reader with shared parsing logic for both file and bytes reading.
Follows the official EDF specification for header parsing.
"""

import re
from typing import BinaryIO


class EDFBaseReader:
    """Base class for EDF header parsing with shared logic."""

    def __init__(self):
        self.hdr = {}

    def parse_header(
        self, fid: BinaryIO, target_signals: list[str] | None = None
    ) -> dict:
        """
        Parse EDF header from a file-like object.

        Args:
            fid: Binary file-like object (file handle or BytesIO)
            target_signals: Optional list of specific signals to extract

        Returns:
            Dictionary containing EDF header information
        """
        try:
            # Main header parsing
            self._parse_main_header(fid)

            # Check if there are signals to parse
            if self.hdr["ns"] == 0:
                self.hdr["label"] = []
                self.hdr["frequency"] = []
                return self.hdr

            # Signal header parsing
            self._parse_signal_headers(fid)

            # Calculate sampling frequencies
            self._calculate_sampling_frequencies()

            # Filter target signals if specified
            if target_signals is not None:
                self._filter_target_signals(target_signals)

            return self.hdr

        except Exception as e:
            # Return default structure on error
            return {
                "label": [],
                "frequency": [],
                "records": 0,
                "duration": 0,
                "startdate": "",
                "starttime": "",
                "ns": 0,
                "error": str(e),
            }

    def _parse_main_header(self, fid: BinaryIO):
        """Parse the main EDF header (256 bytes)."""
        # Version (8 bytes)
        version = fid.read(8).decode("ascii", errors="replace").strip()
        self.hdr["ver"] = int(version) if version.isdigit() else 0

        # Patient ID (80 bytes)
        self.hdr["patientID"] = fid.read(80).decode("ascii", errors="replace").strip()

        # Recording ID (80 bytes)
        self.hdr["recordID"] = fid.read(80).decode("ascii", errors="replace").strip()

        # Start date (8 bytes) - dd.mm.yy format
        self.hdr["startdate"] = fid.read(8).decode("ascii", errors="replace").strip()

        # Start time (8 bytes) - hh.mm.ss format
        self.hdr["starttime"] = fid.read(8).decode("ascii", errors="replace").strip()

        # Header bytes (8 bytes)
        header_bytes = fid.read(8).decode("ascii", errors="replace").strip()
        self.hdr["bytes"] = int(header_bytes) if header_bytes.isdigit() else 256

        # Reserved (44 bytes)
        fid.read(44)

        # Number of records (8 bytes)
        num_records = fid.read(8).decode("ascii", errors="replace").strip()
        self.hdr["records"] = int(num_records) if num_records.isdigit() else 1

        # Duration of a record in seconds (8 bytes)
        duration = fid.read(8).decode("ascii", errors="replace").strip()
        try:
            self.hdr["duration"] = float(duration)
        except ValueError:
            self.hdr["duration"] = 1.0

        # Number of signals (4 bytes)
        num_signals = fid.read(4).decode("ascii", errors="replace").strip()
        self.hdr["ns"] = int(num_signals) if num_signals.isdigit() else 0

    def _parse_signal_headers(self, fid: BinaryIO):
        """Parse signal-specific headers."""
        ns = self.hdr["ns"]

        # Read signal labels (16 bytes each)
        self.hdr["label"] = []
        for _ in range(ns):
            label = fid.read(16).decode("ascii", errors="replace").strip()
            # Clean up the label - remove special characters at the end
            label = re.sub(r"\W+$", "", label)
            self.hdr["label"].append(label)

        # Transducer type (80 bytes each)
        self.hdr["transducer"] = []
        for _ in range(ns):
            self.hdr["transducer"].append(
                fid.read(80).decode("ascii", errors="replace").strip()
            )

        # Physical dimension (8 bytes each)
        self.hdr["units"] = []
        for _ in range(ns):
            self.hdr["units"].append(
                fid.read(8).decode("ascii", errors="replace").strip()
            )

        # Physical minimum (8 bytes each)
        self.hdr["physicalMin"] = []
        for _ in range(ns):
            val = fid.read(8).decode("ascii", errors="replace").strip()
            try:
                self.hdr["physicalMin"].append(float(val))
            except ValueError:
                self.hdr["physicalMin"].append(-32768.0)

        # Physical maximum (8 bytes each)
        self.hdr["physicalMax"] = []
        for _ in range(ns):
            val = fid.read(8).decode("ascii", errors="replace").strip()
            try:
                self.hdr["physicalMax"].append(float(val))
            except ValueError:
                self.hdr["physicalMax"].append(32767.0)

        # Digital minimum (8 bytes each)
        self.hdr["digitalMin"] = []
        for _ in range(ns):
            val = fid.read(8).decode("ascii", errors="replace").strip()
            try:
                self.hdr["digitalMin"].append(int(val))
            except ValueError:
                self.hdr["digitalMin"].append(-32768)

        # Digital maximum (8 bytes each)
        self.hdr["digitalMax"] = []
        for _ in range(ns):
            val = fid.read(8).decode("ascii", errors="replace").strip()
            try:
                self.hdr["digitalMax"].append(int(val))
            except ValueError:
                self.hdr["digitalMax"].append(32767)

        # Prefiltering (80 bytes each)
        self.hdr["prefilter"] = []
        for _ in range(ns):
            self.hdr["prefilter"].append(
                fid.read(80).decode("ascii", errors="replace").strip()
            )

        # Number of samples per record (8 bytes each)
        self.hdr["samples"] = []
        for _ in range(ns):
            val = fid.read(8).decode("ascii", errors="replace").strip()
            try:
                self.hdr["samples"].append(int(val))
            except ValueError:
                self.hdr["samples"].append(256)

        # Skip reserved area (32 bytes per signal)
        for _ in range(ns):
            fid.read(32)

    def _calculate_sampling_frequencies(self):
        """Calculate sampling frequency for each signal."""
        self.hdr["frequency"] = []
        for i in range(self.hdr["ns"]):
            if self.hdr["duration"] > 0:
                self.hdr["frequency"].append(
                    self.hdr["samples"][i] / self.hdr["duration"]
                )
            else:
                self.hdr["frequency"].append(256.0)  # Default sampling rate

    def _filter_target_signals(self, target_signals: list[str]):
        """Filter headers to only include target signals."""
        selected_indices = []
        for i, label in enumerate(self.hdr["label"]):
            if label in target_signals:
                selected_indices.append(i)

        if selected_indices:
            # Filter all arrays to selected signals only
            self.hdr["label"] = [self.hdr["label"][i] for i in selected_indices]
            self.hdr["units"] = [self.hdr["units"][i] for i in selected_indices]
            self.hdr["physicalMin"] = [
                self.hdr["physicalMin"][i] for i in selected_indices
            ]
            self.hdr["physicalMax"] = [
                self.hdr["physicalMax"][i] for i in selected_indices
            ]
            self.hdr["digitalMin"] = [
                self.hdr["digitalMin"][i] for i in selected_indices
            ]
            self.hdr["digitalMax"] = [
                self.hdr["digitalMax"][i] for i in selected_indices
            ]
            self.hdr["prefilter"] = [self.hdr["prefilter"][i] for i in selected_indices]
            self.hdr["transducer"] = [
                self.hdr["transducer"][i] for i in selected_indices
            ]
            self.hdr["samples"] = [self.hdr["samples"][i] for i in selected_indices]
            self.hdr["frequency"] = [self.hdr["frequency"][i] for i in selected_indices]

    def format_info(self) -> dict:
        """Format extracted metadata for frontend consumption."""
        # Get the most common sampling rate
        if self.hdr.get("frequency"):
            sampling_rate = (
                int(self.hdr["frequency"][0]) if self.hdr["frequency"] else 256
            )
        else:
            sampling_rate = 256

        # Calculate total duration
        duration = self.hdr.get("records", 0) * self.hdr.get("duration", 0)

        # Format datetime if available
        start_datetime = None
        if self.hdr.get("startdate") and self.hdr.get("starttime"):
            try:
                start_datetime = f"{self.hdr['startdate']} {self.hdr['starttime']}"
            except Exception:
                start_datetime = None

        return {
            "channels": self.hdr.get("label", []),
            "sampling_rate": sampling_rate,
            "duration_seconds": duration,
            "start_datetime": start_datetime,
            "num_channels": len(self.hdr.get("label", [])),
            "error": self.hdr.get("error"),
        }
