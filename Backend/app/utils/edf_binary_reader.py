"""
EDF reader for file and bytes operations.
Provides simple interfaces for reading EDF headers from files or bytes.
"""

import io

from .edf_base_reader import EDFBaseReader


def extract_edf_metadata(
    filepath: str, target_signals: list[str] | None = None
) -> dict:
    """
    Extract metadata from an EDF file.

    Args:
        filepath: Path to the EDF file
        target_signals: Optional list of specific signals to extract

    Returns:
        Dictionary containing EDF header information including channel labels
    """
    reader = EDFBaseReader()
    with open(filepath, "rb") as fid:
        return reader.parse_header(fid, target_signals)


def extract_edf_metadata_from_bytes(
    data: bytes, target_signals: list[str] | None = None
) -> dict:
    """
    Extract metadata from EDF bytes.

    Args:
        data: Bytes containing at least the EDF header
        target_signals: Optional list of specific signals to extract

    Returns:
        Dictionary containing EDF header information including channel labels
    """
    reader = EDFBaseReader()
    fid = io.BytesIO(data)
    return reader.parse_header(fid, target_signals)


def get_edf_channels(filepath: str) -> list[str]:
    """
    Extract just the channel labels from an EDF file.

    Args:
        filepath: Path to the EDF file

    Returns:
        List of channel labels
    """
    metadata = extract_edf_metadata(filepath)
    return metadata.get("label", [])


def get_edf_info(filepath: str) -> dict:
    """
    Extract basic information needed for the frontend from a file.

    Args:
        filepath: Path to the EDF file

    Returns:
        Dictionary with channels, sampling_rate, duration, etc.
    """
    reader = EDFBaseReader()
    with open(filepath, "rb") as fid:
        reader.parse_header(fid)
    return reader.format_info()


def get_edf_info_from_bytes(data: bytes) -> dict:
    """
    Extract basic information needed for the frontend from bytes.

    Args:
        data: Bytes containing at least the EDF header

    Returns:
        Dictionary with channels, sampling_rate, duration, etc.
    """
    reader = EDFBaseReader()
    fid = io.BytesIO(data)
    reader.parse_header(fid)
    return reader.format_info()
