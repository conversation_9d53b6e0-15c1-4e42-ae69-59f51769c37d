import uuid
from datetime import datetime

from ..constants import EDF_FILES_PREFIX


def generate_file_key(filename: str) -> str:
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = uuid.uuid4().hex[:8]

    # Split filename and extension to ensure .edf stays at the end
    if '.' in filename:
        name_part, ext = filename.rsplit('.', 1)
        return f"{EDF_FILES_PREFIX}/{name_part}_{timestamp}_{unique_id}.{ext}"
    else:
        # If no extension, keep original behavior
        return f"{EDF_FILES_PREFIX}/{filename}_{timestamp}_{unique_id}"


def extract_filename_from_key(key: str) -> str:
    return key.split("/")[-1]
