"""Utility helpers for preparing data for DynamoDB operations."""

from __future__ import annotations

import math
from decimal import Dec<PERSON><PERSON>
from typing import Any

try:  # Optional dependency in some environments
    import numpy as np  # type: ignore

    _NUMERIC_LIKE = (np.generic,)
except Exception:  # pragma: no cover - numpy not available in all runtimes
    _NUMERIC_LIKE: tuple[type[Any], ...] = ()


def _convert_numeric(value: Any) -> Any:
    """Convert numeric values into DynamoDB-compatible representations."""
    if isinstance(value, bool):  # bool is subclass of int; preserve original
        return value

    if isinstance(value, Decimal):
        return value

    if isinstance(value, int):
        return value

    if isinstance(value, float):
        if math.isnan(value) or math.isinf(value):
            return None
        return Decimal(str(value))

    if _NUMERIC_LIKE and isinstance(value, _NUMERIC_LIKE):
        return _convert_numeric(value.item())

    return value


def sanitize_for_dynamodb(value: Any) -> Any:
    """Recursively prepare Python objects for DynamoDB writes.

    - Converts floats (and numpy numeric types) to Decimal instances.
    - Removes values that DynamoDB rejects (None, empty strings, NaN/inf).
    - Normalises containers so nested structures remain JSON-like.
    """
    if value is None:
        return None

    if isinstance(value, str):
        return value if value != "" else None

    if isinstance(value, dict):
        sanitized_dict: dict[str, Any] = {}
        for key, item in value.items():
            sanitized_value = sanitize_for_dynamodb(item)
            if sanitized_value is not None:
                sanitized_dict[key] = sanitized_value
        return sanitized_dict

    if isinstance(value, list | tuple):
        sanitized_list = []
        for item in value:
            sanitized_item = sanitize_for_dynamodb(item)
            if sanitized_item is not None:
                sanitized_list.append(sanitized_item)
        return sanitized_list

    if isinstance(value, set):
        sanitized_set = []
        for item in value:
            sanitized_item = sanitize_for_dynamodb(item)
            if sanitized_item is not None:
                sanitized_set.append(sanitized_item)
        return sanitized_set if sanitized_set else None

    converted_numeric = _convert_numeric(value)
    if converted_numeric is None:
        return None

    return converted_numeric
