"""
Configuration package for backend application.
"""

from .api_config import *
from .aws_config import *
from .file_config import *
from .multipart_config import *
from .s3_config import *

__all__ = [
    # AWS
    "AWS_PROFILE",
    "AWS_REGION",
    "LAMBDA_RESPONSE_HEADERS",
    # API
    "API_PREFIX",
    "API_TITLE",
    "API_DESCRIPTION",
    "API_VERSION",
    # File
    "MAX_FILE_SIZE_MB",
    "MAX_FILE_SIZE_BYTES",
    "ALLOWED_EXTENSIONS",
    "EDF_EXTENSION",
    # S3
    "DEFAULT_EXPIRY_SECONDS",
    "S3_ERROR_NOT_FOUND",
    # Multipart
    "MULTIPART_MIN_FILE_SIZE",
    "MULTIPART_DEFAULT_PART_SIZE",
]
