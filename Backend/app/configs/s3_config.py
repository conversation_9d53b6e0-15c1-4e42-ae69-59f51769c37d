"""
S3-specific configuration settings.
"""

# S3 Paths and Prefixes
S3_PREFIX = "edf-files/"
METADATA_ORIGINAL_FILENAME = "original-filename"

# S3 Client Methods
S3_CLIENT_METHOD_PUT = "put_object"
S3_CLIENT_METHOD_GET = "get_object"
S3_CLIENT_METHOD_UPLOAD_PART = "upload_part"
S3_LIST_OBJECTS_METHOD = "list_objects_v2"

# S3 Error Codes
S3_ERROR_NOT_FOUND = "404"

# Presigned URL Settings
DEFAULT_EXPIRY_SECONDS = 3600
PRESIGNED_URL_EXPIRY_SECONDS = 3600

# S3 CORS Configuration
S3_CORS_MAX_AGE_SECONDS = 3600
S3_MULTIPART_UPLOAD_CLEANUP_DAYS = 1
S3_CORS_EXPOSED_HEADERS = [
    "ETag",
    "x-amz-server-side-encryption",
    "x-amz-request-id",
    "x-amz-id-2",
]
