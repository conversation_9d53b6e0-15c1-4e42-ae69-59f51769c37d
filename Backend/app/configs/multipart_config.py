"""
Multipart upload configuration.
"""

# Size Thresholds
MULTIPART_MIN_FILE_SIZE = 5 * 1024 * 1024  # 5MB - Use multipart for files larger than this

# Part Size Limits
MULTIPART_MIN_PART_SIZE = 5 * 1024 * 1024  # 5MB minimum part size
MULTIPART_MAX_PART_SIZE = 100 * 1024 * 1024  # 100MB maximum part size
MULTIPART_DEFAULT_PART_SIZE = 10 * 1024 * 1024  # 10MB default part size
MULTIPART_MAX_PARTS = 10000  # AWS S3 limit

# Retry Configuration
MULTIPART_URL_EXPIRY_SECONDS = 3600  # 1 hour
MULTIPART_MAX_RETRIES = 3
MULTIPART_RETRY_DELAY_SECONDS = 0.5
MULTIPART_MAX_RETRY_DELAY_SECONDS = 8.0
MULTIPART_BACKOFF_MULTIPLIER = 2

# Dynamic Sizing Based on File Size
MULTIPART_SMALL_FILE_THRESHOLD = 100 * 1024 * 1024  # 100MB
MULTIPART_MEDIUM_FILE_THRESHOLD = 500 * 1024 * 1024  # 500MB
MULTIPART_SMALL_FILE_CHUNK_SIZE = 5 * 1024 * 1024  # 5MB chunks for <100MB
MULTIPART_MEDIUM_FILE_CHUNK_SIZE = 10 * 1024 * 1024  # 10MB chunks for 100-500MB
MULTIPART_LARGE_FILE_CHUNK_SIZE = 20 * 1024 * 1024  # 20MB chunks for >500MB

# Concurrency Settings
MULTIPART_SMALL_FILE_CONCURRENCY = 3  # 3 parallel uploads for <100MB
MULTIPART_MEDIUM_FILE_CONCURRENCY = 5  # 5 parallel uploads for 100-500MB
MULTIPART_LARGE_FILE_CONCURRENCY = 8  # 8 parallel uploads for >500MB
MULTIPART_MAX_CONCURRENCY = 10  # Maximum concurrent part uploads
