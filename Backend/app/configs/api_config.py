"""
API and network configuration.
"""

# API Settings
API_PREFIX = "/api/v1"
API_TITLE = "Biormika EDF File Upload API"
API_DESCRIPTION = "API for uploading and managing EDF files for HFO analysis"
API_VERSION = "1.0.0"

# Network Ports
VITE_DEV_PORT = 5173
REACT_ALT_PORT = 3000
FASTAPI_PORT = 8000

# CORS Configuration
DEFAULT_ALLOWED_ORIGINS = [
    f"http://localhost:{VITE_DEV_PORT}",
    f"http://localhost:{REACT_ALT_PORT}",
    f"http://localhost:{FASTAPI_PORT}",
]
CORS_ALLOW_CREDENTIALS = True
CORS_ALLOW_METHODS = ["*"]
CORS_ALLOW_HEADERS = ["*"]

# HTTP Status Codes
HTTP_STATUS_OK = 200
HTTP_STATUS_BAD_REQUEST = 400
HTTP_STATUS_NOT_FOUND = 404
HTTP_STATUS_INTERNAL_SERVER_ERROR = 500

# Logging Configuration
LOGGING_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
LOGGING_LEVEL = "INFO"
