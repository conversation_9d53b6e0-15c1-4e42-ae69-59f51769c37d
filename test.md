# API Testing Reference

## HFO Analysis Endpoints

### 1. Batch Analysis Endpoint
**URL:** `http://localhost:8000/api/v1/analysis/batch`
**Method:** `POST`
**Content-Type:** `application/json`

#### Test Payload (Full Channel Set)
```json
{
    "files": [
        {
            "file_key": "edf-files/20250924_214746_b45eb3dd_15.edf",
            "parameters": {
                "thresholds": {
                    "amplitude1": 2,
                    "amplitude2": 2,
                    "peaks1": 6,
                    "peaks2": 3,
                    "duration": 10,
                    "temporal_sync": 10,
                    "spatial_sync": 10
                },
                "montage": "bipolar",
                "frequency": {
                    "low_cutoff": 50,
                    "high_cutoff": 300
                },
                "analysis_start": 0,
                "analysis_end": 0,
                "channelSelection": {
                    "selectedChannels": [
                        "P RG1", "P RG2", "P RG3", "P RG4", "P RG5", "P RG6", "P RG7", "P RG8", "P RG9", "P RG10",
                        "P RG13", "P RG14", "P RG15", "P RG16", "P RG17", "P RG18", "P RG19", "P RG20", "P RG21", "P E",
                        "P RG22", "P RG23", "P RG11", "P RG12", "P RG24", "P RG25", "P RG26", "P RG27", "P RG28", "P RG29",
                        "P RG30", "P RG31", "P RG32", "P RG33", "P RG34", "P RG35", "P RG36", "P DC01", "P DC02", "P DC03",
                        "P DC04", "P RG37", "P RG38", "P MARK1", "P MARK2", "P RG39", "P RG40", "P RG41", "P RG42", "P RG43",
                        "P RG44", "P RG45", "P RG46", "P RG47", "P RG48", "P SF1", "P SF2", "P SF3", "P SF4", "P SF5",
                        "P SF6", "P SF7", "P SF8", "P SF9", "P SF10", "P SF11", "P SF12", "P SF13", "P SF14", "P SF15",
                        "P SF16", "P RT1", "P RT2", "P RT3", "P RT4", "P RT5", "P RT6", "P RT7", "P RT8", "P RT9",
                        "P RT10", "P RT11", "P RT12", "P RT13", "P RT14", "P RT15", "P RT16", "P RD1", "P RD2", "P RD3",
                        "P RD4", "P RD5", "P RD6", "P RD7", "P RD8", "P ST1", "P ST2", "P ST3", "P ST4", "P ST5",
                        "P ST6", "P ST7", "P ST8", "P IH5", "P IH6", "P IH7", "P IH8", "P IH9", "P IH10", "P IH11",
                        "P IH12", "P IH13", "P IH14", "P IH15", "P IH16", "P IH17", "P IH18", "P IH19", "P IH20", "P IN1",
                        "P IN2", "P IN3", "P IN4", "P LF1", "P LF2", "P LF3", "P LF4", "P LF5", "P LF6", "P LF7",
                        "P LF8", "P EKG", "P EKG", "P REF", "EDF Annotations"
                    ]
                }
            }
        }
    ]
}
```

#### Test Commands

**Using curl:**
```bash
# Save payload to file
cat > /tmp/test_payload.json << 'EOF'
# paste the JSON payload above
EOF

# Submit batch analysis
curl -X POST http://localhost:8000/api/v1/analysis/batch \
  -H "Content-Type: application/json" \
  -d @/tmp/test_payload.json | python3 -m json.tool
```

**Direct curl (simplified payload):**
```bash
curl -X POST http://localhost:8000/api/v1/analysis/batch \
  -H "Content-Type: application/json" \
  -d '{
    "files": [{
        "file_key": "edf-files/20250924_214746_b45eb3dd_15.edf",
        "parameters": {
            "thresholds": {
                "amplitude1": 2,
                "amplitude2": 2,
                "peaks1": 6,
                "peaks2": 3,
                "duration": 10,
                "temporal_sync": 10,
                "spatial_sync": 10
            },
            "montage": "bipolar",
            "frequency": {
                "low_cutoff": 50,
                "high_cutoff": 300
            },
            "analysis_start": 0,
            "analysis_end": 0
        }
    }]
}' | python3 -m json.tool
```

### 2. Single Analysis Endpoint
**URL:** `http://localhost:8000/api/v1/analysis/submit`
**Method:** `POST`
**Content-Type:** `application/json`

```json
{
    "file_key": "edf-files/20250924_214746_b45eb3dd_15.edf",
    "parameters": {
        "thresholds": {
            "amplitude1": 2,
            "amplitude2": 2,
            "peaks1": 6,
            "peaks2": 3,
            "duration": 10,
            "temporal_sync": 10,
            "spatial_sync": 10
        },
        "montage": "bipolar",
        "frequency": {
            "low_cutoff": 50,
            "high_cutoff": 300
        },
        "analysis_start": 0,
        "analysis_end": 0
    }
}
```

### 3. Check Job Status
**URL:** `http://localhost:8000/api/v1/analysis/jobs/{job_id}`
**Method:** `GET`

```bash
# Replace {job_id} with actual job ID from submission response
curl http://localhost:8000/api/v1/analysis/jobs/{job_id}
```

### 4. Get Analysis Results
**URL:** `http://localhost:8000/api/v1/analysis/jobs/{job_id}/results`
**Method:** `GET`

```bash
curl http://localhost:8000/api/v1/analysis/jobs/{job_id}/results
```

## Monitoring Commands

### Check AWS Logs
```bash
# Recent ECS logs
aws logs tail /ecs/biormika-hfo-processor --profile biormika --since 5m

# Follow logs in real-time
aws logs tail /ecs/biormika-hfo-processor --profile biormika --follow

# Check Lambda logs
aws logs tail /aws/lambda/BiormikaStack-ApiFunction --profile biormika --since 5m
```

### Check ECS Task Status
```bash
# List running tasks
aws ecs list-tasks \
  --cluster biormika-hfo-cluster \
  --service-name biormika-hfo-processor \
  --profile biormika

# Describe service
aws ecs describe-services \
  --cluster biormika-hfo-cluster \
  --services biormika-hfo-processor \
  --profile biormika
```

### Check SQS Queue
```bash
# Get queue URL
aws sqs list-queues --profile biormika | grep biormika-hfo

# Check queue attributes (message count)
aws sqs get-queue-attributes \
  --queue-url <queue-url> \
  --attribute-names All \
  --profile biormika
```

### Check S3 Results
```bash
# List results for a job
aws s3 ls s3://biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv/results/{job_id}/ --profile biormika

# Download results
aws s3 cp s3://biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv/results/{job_id}/analysis_results.json . --profile biormika
```

## Parameter Reference

### Thresholds
- `amplitude1`: Amplitude threshold for HFO detection (default: 2)
- `amplitude2`: Secondary amplitude threshold (default: 2)
- `peaks1`: Minimum number of peaks required (default: 6)
- `peaks2`: Secondary peak threshold (default: 3)
- `duration`: Minimum duration in milliseconds (default: 10)
- `temporal_sync`: Temporal synchronization window (default: 10)
- `spatial_sync`: Spatial synchronization window (default: 10)

### Montage Options
- `"bipolar"`: Bipolar montage
- `"referential"`: Referential montage
- `"common_average"`: Common average reference

### Frequency
- `low_cutoff`: High-pass filter frequency in Hz (default: 50)
- `high_cutoff`: Low-pass filter frequency in Hz (default: 300)

### Analysis Window
- `analysis_start`: Start time in seconds (0 = beginning)
- `analysis_end`: End time in seconds (0 = end of file)

## Common Test Scenarios

### 1. Quick Test (Minimal Parameters)
```bash
curl -X POST http://localhost:8000/api/v1/analysis/submit \
  -H "Content-Type: application/json" \
  -d '{
    "file_key": "edf-files/20250924_214746_b45eb3dd_15.edf",
    "parameters": {
        "montage": "bipolar"
    }
}'
```

### 2. High Sensitivity Test
```bash
curl -X POST http://localhost:8000/api/v1/analysis/submit \
  -H "Content-Type: application/json" \
  -d '{
    "file_key": "edf-files/20250924_214746_b45eb3dd_15.edf",
    "parameters": {
        "thresholds": {
            "amplitude1": 1.5,
            "amplitude2": 1.5,
            "peaks1": 4,
            "peaks2": 2
        },
        "montage": "bipolar"
    }
}'
```

### 3. Multiple Files Batch
```bash
curl -X POST http://localhost:8000/api/v1/analysis/batch \
  -H "Content-Type: application/json" \
  -d '{
    "files": [
        {
            "file_key": "edf-files/file1.edf",
            "parameters": {"montage": "bipolar"}
        },
        {
            "file_key": "edf-files/file2.edf",
            "parameters": {"montage": "referential"}
        }
    ]
}'
```

## Troubleshooting

### Check if Backend is Running
```bash
curl http://localhost:8000/health
```

### Check if File Exists in S3
```bash
aws s3 ls s3://biormikastack-s3storageedfstoragebucket8b55a415-vt2ocke56liv/edf-files/ --profile biormika
```

### Force ECS Service Restart
```bash
aws ecs update-service \
  --cluster biormika-hfo-cluster \
  --service biormika-hfo-processor \
  --force-new-deployment \
  --profile biormika
```

## Notes
- Replace `{job_id}` with actual job IDs from API responses
- The S3 bucket name may vary based on your deployment
- Ensure AWS profile `biormika` is configured with appropriate credentials
- Processing time varies based on file size and parameters (typically 5-30 seconds)